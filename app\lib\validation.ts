/**
 * Excel验证服务 - 基于官方Univer API
 */

export interface ValidationRule {
  type: string
  cell?: string
  expectedValue?: unknown
  expectedFormula?: string
  expectedFormat?: string
  expectedStyle?: Record<string, unknown>
  dataRange?: string
  expectedType?: string
  expectedFields?: string[]
  // 透视表严格验证相关字段
  strictValidation?: boolean
  expectedRowHeaders?: string[]
  expectedColumnHeaders?: string[]
  expectedTotalValue?: number
  // 筛选验证相关字段
  expectedVisibleRows?: number
  expectedFilteredData?: Array<Record<string, unknown>>
  // 排序验证相关字段
  expectedOrder?: string[] | number[]
  sortColumn?: string
  sortDirection?: 'asc' | 'desc'
  // 条件格式验证相关字段
  expectedBackgroundColor?: string
  expectedTextColor?: string
  conditionRange?: string
  // 兼容旧的字段名
  column?: string
  direction?: 'asc' | 'desc'
  sorts?: Array<{ column: string; direction: 'asc' | 'desc' }>
  range?: string
  condition?: string
  value?: any
  expectedFormattedCells?: string[]
  conditions?: Array<{ type: string; value?: any; minValue?: any; maxValue?: any; color: string }>
  expectedResults?: Record<string, string[]>
  formulaCell?: string
  expectedResult?: unknown
}

export interface ValidationResult {
  success: boolean
  message: string
  details?: Record<string, unknown>
}

export interface UniverAPI {
  getActiveWorkbook(): any
}

export class ExcelValidationService {
  constructor(private univerAPI: UniverAPI) {}

  /**
   * 验证任务
   */
  async validateTask(rule: ValidationRule): Promise<ValidationResult> {
    console.log('开始验证任务:', rule)

    try {
      switch (rule.type) {
        case 'cellValue':
        case 'input': // input类型等同于cellValue验证
          return await this.validateCellValue(rule)
        case 'cellFormula':
          return await this.validateCellFormula(rule)
        case 'cellFormat':
          return await this.validateCellFormat(rule)
        case 'cellStyle':
          return await this.validateCellStyle(rule)
        case 'chart':
          return await this.validateChart(rule)
        case 'pivotTable':
          return await this.validatePivotTable(rule)
        case 'filter':
          return await this.validateFilter(rule)
        case 'sort':
          return await this.validateSort(rule)
        case 'multiSort':
          return await this.validateMultiSort(rule)
        case 'conditional_format':
        case 'conditionalFormat':
        case 'multiConditionalFormat':
          return await this.validateConditionalFormat(rule)
        default:
          return {
            success: false,
            message: `未知的验证类型: ${rule.type}`
          }
      }
    } catch (error) {
      console.error('验证过程中发生错误:', error)
      return {
        success: false,
        message: '验证过程中发生错误，请重试',
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格值
   */
  private async validateCellValue(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || rule.expectedValue === undefined) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望值'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return {
          success: false,
          message: '无法获取活动工作簿'
        }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return {
          success: false,
          message: '无法获取活动工作表'
        }
      }

      const range = worksheet.getRange(rule.cell)
      if (!range) {
        return {
          success: false,
          message: `无法获取单元格 ${rule.cell}`
        }
      }

      const cellValue = range.getValue()
      const actualValue = cellValue?.v || cellValue // 处理Univer的值格式

      // 类型转换和比较
      const expectedValue = rule.expectedValue
      const isMatch = this.compareValues(actualValue, expectedValue)

      return {
        success: isMatch,
        message: isMatch
          ? '单元格值验证通过！'
          : `单元格 ${rule.cell} 的值不正确。期望: "${expectedValue}"，实际: "${actualValue}"`,
        details: {
          cell: rule.cell,
          expected: expectedValue,
          actual: actualValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证单元格值时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格公式
   */
  private async validateCellFormula(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedFormula) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望公式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      const range = worksheet.getRange(rule.cell)

      // 获取单元格公式
      const cellData = range.getCellData()
      const actualFormula = cellData?.f || ''

      // 标准化公式格式（去除空格，统一大小写）
      const normalizeFormula = (formula: string) => {
        return formula.replace(/\s+/g, '').toUpperCase()
      }

      const expectedNormalized = normalizeFormula(rule.expectedFormula)
      const actualNormalized = normalizeFormula(actualFormula)

      const isFormulaMatch = actualNormalized === expectedNormalized

      // 如果有期望值，也验证计算结果
      let isValueMatch = true
      let actualValue = null
      if (rule.expectedValue !== undefined) {
        const cellValue = range.getValue()
        actualValue = cellValue?.v || cellValue
        isValueMatch = this.compareValues(actualValue, rule.expectedValue)
      }

      const success = isFormulaMatch && isValueMatch

      let message = ''
      if (!isFormulaMatch) {
        message = `公式不正确。期望: "${rule.expectedFormula}"，实际: "${actualFormula}"`
      } else if (!isValueMatch) {
        message = `公式正确但计算结果不对。期望结果: ${rule.expectedValue}，实际结果: ${actualValue}`
      } else {
        message = '公式验证通过！'
      }

      return {
        success,
        message,
        details: {
          cell: rule.cell,
          expectedFormula: rule.expectedFormula,
          actualFormula,
          expectedValue: rule.expectedValue,
          actualValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证公式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格格式
   */
  private async validateCellFormat(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedFormat) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望格式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      const range = worksheet.getRange(rule.cell)

      // 获取单元格格式信息
      const cellData = range.getCellData()
      const numberFormat = range.getNumberFormat() || cellData?.s?.n?.pattern || ''

      // 获取单元格的显示值
      const cellValue = range.getValue()
      const displayValue = cellValue?.v !== undefined ? cellValue.v.toString() : cellValue?.toString() || ''

      // 根据期望格式类型进行验证
      let isFormatMatch = false
      let formatDescription = ''

      switch (rule.expectedFormat) {
        case 'currency':
          // 检查格式字符串或显示值是否包含货币符号
          isFormatMatch = this.isCurrencyFormat(numberFormat) || this.isCurrencyFormat(displayValue)
          formatDescription = '货币格式'
          break
        case 'percentage':
          isFormatMatch = this.isPercentageFormat(numberFormat) || this.isPercentageFormat(displayValue)
          formatDescription = '百分比格式'
          break
        case 'date':
          isFormatMatch = this.isDateFormat(numberFormat) || this.isDateFormat(displayValue)
          formatDescription = '日期格式'
          break
        default:
          isFormatMatch = numberFormat === rule.expectedFormat
          formatDescription = rule.expectedFormat
      }

      return {
        success: isFormatMatch,
        message: isFormatMatch
          ? `${formatDescription}验证通过！`
          : `单元格 ${rule.cell} 的格式不正确。期望: ${formatDescription}，实际格式: "${numberFormat}"，显示值: "${displayValue}"`,
        details: {
          cell: rule.cell,
          expectedFormat: rule.expectedFormat,
          actualFormat: numberFormat,
          displayValue: displayValue
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证格式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证单元格样式
   */
  private async validateCellStyle(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.cell || !rule.expectedStyle) {
      return {
        success: false,
        message: '验证规则配置错误：缺少单元格位置或期望样式'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()
      const range = worksheet.getRange(rule.cell)

      // 获取单元格样式信息
      const style = range.getCellStyleData() || {}

      const validationResults = []

      // 验证粗体
      if (rule.expectedStyle.bold !== undefined) {
        // 只检查标准的粗体属性，避免过于宽松的验证
        const isBold = style.bl === 1 || style.bl === true ||
                      style.bold === 1 || style.bold === true ||
                      style.fontWeight === 'bold' || style.fontWeight === 700 ||
                      (style.ft && (style.ft.bl === 1 || style.ft.bl === true))
        const expectedBold = rule.expectedStyle.bold

        console.log('粗体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isBold: isBold,
          expectedBold: expectedBold,
          styleKeys: Object.keys(style),
          styleType: typeof style
        })

        validationResults.push({
          property: 'bold',
          expected: expectedBold,
          actual: isBold,
          match: isBold === expectedBold
        })
      }

      // 验证斜体
      if (rule.expectedStyle.italic !== undefined) {
        // 支持多种斜体属性格式
        const isItalic = style.it === 1 || style.it === true ||
                         style.italic === 1 || style.italic === true ||
                         style.fontStyle === 'italic' ||
                         (style.ft && (style.ft.it === 1 || style.ft.it === true))
        const expectedItalic = rule.expectedStyle.italic

        console.log('斜体验证调试信息:', {
          cell: rule.cell,
          style: style,
          isItalic: isItalic,
          expectedItalic: expectedItalic,
          styleKeys: Object.keys(style)
        })

        validationResults.push({
          property: 'italic',
          expected: expectedItalic,
          actual: isItalic,
          match: isItalic === expectedItalic
        })
      }

      // 验证字体颜色
      if (rule.expectedStyle.color) {
        const actualColor = style.cl?.rgb || ''
        const expectedColor = rule.expectedStyle.color
        validationResults.push({
          property: 'color',
          expected: expectedColor,
          actual: actualColor,
          match: actualColor === expectedColor
        })
      }

      // 验证背景色
      if (rule.expectedStyle.backgroundColor) {
        const actualBgColor = style.bg?.rgb || ''
        const expectedBgColor = rule.expectedStyle.backgroundColor
        validationResults.push({
          property: 'backgroundColor',
          expected: expectedBgColor,
          actual: actualBgColor,
          match: actualBgColor === expectedBgColor
        })
      }

      const allMatch = validationResults.every(result => result.match)
      const failedValidations = validationResults.filter(result => !result.match)

      let message = ''
      if (allMatch) {
        message = '样式验证通过！'
      } else {
        const failedDetails = failedValidations.map(v =>
          `${v.property}(期望:${v.expected}, 实际:${v.actual})`
        ).join('、')
        message = `单元格 ${rule.cell} 的样式不正确。失败的属性: ${failedDetails}。当前样式对象: ${JSON.stringify(style)}`
      }

      return {
        success: allMatch,
        message,
        details: {
          cell: rule.cell,
          validationResults,
          actualStyle: style
        }
      }
    } catch (error) {
      return {
        success: false,
        message: `验证样式时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证图表
   */
  private async validateChart(rule: ValidationRule): Promise<ValidationResult> {
    try {
      // 简化的图表验证逻辑
      // 检查是否选择了正确的数据范围
      if (rule.dataRange) {
        const workbook = this.univerAPI.getActiveWorkbook()
        const worksheet = workbook.getActiveSheet()

        // 验证数据范围是否有数据
        const range = worksheet.getRange(rule.dataRange)
        const values = range.getValues()

        if (!values || values.length === 0) {
          return {
            success: false,
            message: `数据范围 ${rule.dataRange} 中没有数据。请确保数据范围正确。`,
            details: {
              dataRange: rule.dataRange,
              expectedType: rule.expectedType
            }
          }
        }
      }

      // 检查DOM中是否存在图表相关元素
      const chartSelectors = [
        '.univer-chart',
        '.echarts-chart',
        '.chart-container',
        '[data-chart-id]',
        'canvas[data-zr-dom-id]', // ECharts canvas
        '.univer-drawing-object' // Univer绘图对象
      ]

      let chartFound = false
      for (const selector of chartSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          chartFound = true
          console.log(`找到图表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }

      if (!chartFound) {
        return {
          success: false,
          message: '未找到图表。请按照操作步骤创建图表：\n1. 选择数据范围\n2. 点击"插入"选项卡\n3. 选择"图表"\n4. 选择合适的图表类型',
          details: {
            expectedType: rule.expectedType,
            dataRange: rule.dataRange,
            hint: '请确保已经插入了图表'
          }
        }
      }

      return {
        success: true,
        message: '图表创建成功！任务完成。',
        details: {
          expectedType: rule.expectedType,
          dataRange: rule.dataRange,
          chartFound: true
        }
      }

    } catch (error) {
      console.error('图表验证错误:', error)
      return {
        success: false,
        message: `图表验证时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证数据透视表
   */
  private async validatePivotTable(rule: ValidationRule): Promise<ValidationResult> {
    try {
      console.log('开始验证透视表:', rule)

      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()

      // 如果启用严格验证，需要验证具体的字段配置
      if (rule.strictValidation) {
        return await this.validatePivotTableStrict(rule, worksheet)
      }

      // 基础验证：检查是否创建了透视表
      const basicValidation = await this.validatePivotTableBasic(worksheet)

      if (!basicValidation.success) {
        return basicValidation
      }

      return {
        success: true,
        message: '数据透视表创建成功！任务完成。',
        details: {
          expectedFields: rule.expectedFields,
          validationType: 'basic'
        }
      }

    } catch (error) {
      console.error('透视表验证错误:', error)
      return {
        success: false,
        message: `透视表验证时发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 验证筛选功能 - 基于官方FFilter API
   */
  private async validateFilter(rule: ValidationRule): Promise<ValidationResult> {
    if (!rule.dataRange) {
      return {
        success: false,
        message: '验证规则配置错误：缺少数据范围'
      }
    }

    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()

      console.log('筛选验证 - 开始基于官方FFilter API的验证')

      // 使用官方FFilter API进行验证
      const filter = worksheet.getFilter()
      console.log('筛选验证 - 获取筛选器:', filter)

      if (!filter) {
        return {
          success: false,
          message: '未检测到筛选器。请确保已正确执行以下步骤：\n\n1. 选择数据范围（包含表头）\n2. 启用筛选功能（数据 → 筛选）\n3. 设置筛选条件\n\n提示：筛选功能会在表头显示下拉箭头。',
          details: {
            filterDetected: false,
            hint: '请先启用筛选功能'
          }
        }
      }

      // 获取筛选范围
      const filterRange = filter.getRange()
      console.log('筛选验证 - 筛选范围:', filterRange?.getA1Notation())

      // 获取被筛选掉的行
      const filteredOutRows = filter.getFilteredOutRows()
      console.log('筛选验证 - 被筛选掉的行:', filteredOutRows)

      // 检查是否有筛选条件被设置
      let hasFilterCriteria = false
      const filterCriteriaDetails: any = {}

      if (filterRange) {
        const rangeNotation = filterRange.getA1Notation()
        const rangeMatch = rangeNotation.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/)

        if (rangeMatch) {
          const startCol = rangeMatch[1]
          const endCol = rangeMatch[3]
          const startColIndex = this.getColumnIndex(startCol)
          const endColIndex = this.getColumnIndex(endCol)

          for (let colIndex = startColIndex; colIndex <= endColIndex; colIndex++) {
            try {
              const criteria = filter.getColumnFilterCriteria(colIndex)
              if (criteria) {
                hasFilterCriteria = true
                filterCriteriaDetails[`column_${colIndex}`] = criteria
                console.log(`筛选验证 - 列${colIndex}的筛选条件:`, criteria)
              }
            } catch (error) {
              console.log(`筛选验证 - 获取列${colIndex}筛选条件失败:`, error)
            }
          }
        }
      }

      // 验证筛选是否真正应用
      if (!hasFilterCriteria) {
        return {
          success: false,
          message: '未检测到筛选条件。请确保已正确设置筛选条件：\n\n1. 点击表头的下拉箭头\n2. 取消选中"全选"\n3. 只勾选需要显示的值\n4. 点击"确定"\n\n提示：设置筛选条件后，部分行会被隐藏。',
          details: {
            filterDetected: true,
            hasFilterCriteria: false,
            filterRange: filterRange?.getA1Notation(),
            hint: '筛选器已启用但未设置筛选条件'
          }
        }
      }

      // 验证筛选条件是否符合预期
      if (rule.expectedFilteredData && rule.expectedFilteredData.length > 0) {
        const criteriaValidation = await this.validateFilterCriteria(filterCriteriaDetails, rule)
        if (!criteriaValidation.isValid) {
          return {
            success: false,
            message: criteriaValidation.message || '筛选条件不符合预期',
            details: {
              filterDetected: true,
              hasFilterCriteria: true,
              filterCriteria: filterCriteriaDetails,
              criteriaValidation: criteriaValidation,
              hint: '筛选条件设置不正确'
            }
          }
        }
      }

      // 验证筛选结果
      if (filteredOutRows && filteredOutRows.length > 0) {
        console.log('筛选验证 - 检测到筛选结果，有行被筛选掉')

        // 验证被筛选掉的行数是否合理
        if (rule.expectedVisibleRows !== undefined) {
          const range = worksheet.getRange(rule.dataRange)
          const values = range.getValues()
          const totalRows = values.length - 1 // 减去表头
          const expectedFilteredOutRows = totalRows - rule.expectedVisibleRows

          if (filteredOutRows.length !== expectedFilteredOutRows) {
            return {
              success: false,
              message: `筛选结果不正确。期望显示 ${rule.expectedVisibleRows} 行数据，但实际筛选掉了 ${filteredOutRows.length} 行（应该筛选掉 ${expectedFilteredOutRows} 行）。\n\n请检查筛选条件设置。`,
              details: {
                filterDetected: true,
                hasFilterCriteria: true,
                totalRows: totalRows,
                expectedVisibleRows: rule.expectedVisibleRows,
                expectedFilteredOutRows: expectedFilteredOutRows,
                actualFilteredOutRows: filteredOutRows.length,
                filteredOutRows: filteredOutRows,
                hint: '筛选结果行数不正确'
              }
            }
          }
        }
      } else {
        // 没有行被筛选掉，可能筛选条件包含了所有数据
        console.log('筛选验证 - 没有行被筛选掉，检查筛选条件是否合理')

        if (rule.expectedVisibleRows !== undefined) {
          const range = worksheet.getRange(rule.dataRange)
          const values = range.getValues()
          const totalRows = values.length - 1 // 减去表头

          if (totalRows !== rule.expectedVisibleRows) {
            return {
              success: false,
              message: `筛选条件可能设置不正确。期望显示 ${rule.expectedVisibleRows} 行数据，但当前显示 ${totalRows} 行。\n\n请检查筛选条件是否正确设置。`,
              details: {
                filterDetected: true,
                hasFilterCriteria: true,
                totalRows: totalRows,
                expectedVisibleRows: rule.expectedVisibleRows,
                filteredOutRows: [],
                hint: '筛选条件可能包含了所有数据'
              }
            }
          }
        }
      }

      return {
        success: true,
        message: '筛选验证通过！筛选功能使用正确。',
        details: {
          filterDetected: true,
          hasFilterCriteria: true,
          filterRange: filterRange?.getA1Notation(),
          filterCriteria: filterCriteriaDetails,
          filteredOutRows: filteredOutRows || [],
          hint: '筛选功能使用正确'
        }
      }

    } catch (error) {
      console.error('筛选验证错误:', error)
      return {
        success: false,
        message: `验证筛选时发生错误: ${error}`,
        details: { error: String(error) }
      }
    }
  }

  /**
   * 验证筛选条件是否符合预期
   */
  private async validateFilterCriteria(filterCriteria: any, rule: ValidationRule): Promise<{ isValid: boolean; message?: string }> {
    try {
      if (!rule.expectedFilteredData || rule.expectedFilteredData.length === 0) {
        return { isValid: true }
      }

      // 分析期望数据的筛选模式
      const expectedPatterns: any = {}

      for (const row of rule.expectedFilteredData) {
        for (const [colKey, value] of Object.entries(row)) {
          const colIndex = parseInt(colKey)
          if (!expectedPatterns[colIndex]) {
            expectedPatterns[colIndex] = new Set()
          }
          expectedPatterns[colIndex].add(value)
        }
      }

      console.log('筛选条件验证 - 期望的筛选模式:', expectedPatterns)
      console.log('筛选条件验证 - 实际的筛选条件:', filterCriteria)

      // 检查筛选条件是否与期望模式匹配
      for (const [colKey, criteria] of Object.entries(filterCriteria)) {
        const colIndex = parseInt(colKey.split('_')[1])
        const expectedValues = expectedPatterns[colIndex]

        if (expectedValues && criteria && (criteria as any).filters && (criteria as any).filters.filters) {
          const actualFilters = new Set((criteria as any).filters.filters)
          const expectedFiltersArray = Array.from(expectedValues)

          // 检查筛选条件是否包含期望的值
          let hasExpectedValues = false
          for (const expectedValue of expectedFiltersArray) {
            if (actualFilters.has(String(expectedValue))) {
              hasExpectedValues = true
              break
            }
          }

          if (!hasExpectedValues) {
            return {
              isValid: false,
              message: `列${colIndex + 1}的筛选条件不正确。期望包含值: ${expectedFiltersArray.join(', ')}，实际筛选条件: ${Array.from(actualFilters).join(', ')}`
            }
          }
        }
      }

      return { isValid: true }
    } catch (error) {
      console.log('筛选条件验证失败:', error)
      return { isValid: true } // 验证失败时默认通过，避免误判
    }
  }

  /**
   * 获取列索引（A=0, B=1, C=2...）
   */
  private getColumnIndex(column: string): number {
    let result = 0
    for (let i = 0; i < column.length; i++) {
      result = result * 26 + (column.charCodeAt(i) - 'A'.charCodeAt(0) + 1)
    }
    return result - 1
  }

  /**
   * 比较两个值是否相等
   */
  private compareValues(actual: unknown, expected: unknown): boolean {
    // 处理数字比较
    if (typeof expected === 'number' && typeof actual === 'number') {
      return Math.abs(actual - expected) < 0.0001 // 浮点数比较
    }

    // 处理字符串比较（忽略大小写和前后空格）
    if (typeof expected === 'string' && typeof actual === 'string') {
      return actual.trim().toLowerCase() === expected.trim().toLowerCase()
    }

    // 其他类型直接比较
    return actual === expected
  }

  /**
   * 验证单列排序
   */
  private async validateSort(rule: ValidationRule): Promise<ValidationResult> {
    try {
      console.log('开始验证单列排序:', rule)

      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return { success: false, message: '无法获取工作簿' }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return { success: false, message: '无法获取工作表' }
      }

      // 确定数据范围，如果没有指定则使用默认范围
      const dataRange = rule.dataRange || 'A1:C6'
      const range = worksheet.getRange(dataRange)
      const values = range.getValues()

      if (!values || values.length < 2) {
        return {
          success: false,
          message: '数据范围内没有足够的数据进行排序验证。请确保数据范围内有数据并且已经进行了排序操作。'
        }
      }

      console.log('获取到的数据:', values)

      // 如果有期望的顺序，直接验证是否匹配（更可靠的方法）
      if (rule.expectedOrder && rule.expectedOrder.length > 0) {
        // 跳过表头，从第二行开始验证
        const dataRows = values.slice(1)
        const actualOrder = dataRows.map((row: any) => row[0]) // 第一列是标识列（姓名）

        console.log('期望顺序:', rule.expectedOrder)
        console.log('实际顺序:', actualOrder)

        const isOrderCorrect = this.checkExpectedOrder(actualOrder, rule.expectedOrder)

        if (!isOrderCorrect) {
          return {
            success: false,
            message: `排序结果与期望顺序不匹配。\n期望顺序: ${rule.expectedOrder.join(', ')}\n实际顺序: ${actualOrder.join(', ')}\n\n请按照以下步骤进行排序：\n1. 选择数据范围${dataRange}\n2. 点击"数据"选项卡\n3. 点击"排序"\n4. 选择按${rule.column || rule.sortColumn}列进行${rule.direction === 'desc' ? '降序' : '升序'}排序\n5. 点击"确定"`
          }
        }

        return {
          success: true,
          message: '排序验证通过！数据已按期望顺序排列。',
          details: {
            expectedOrder: rule.expectedOrder,
            actualOrder: actualOrder,
            sortColumn: rule.column || rule.sortColumn,
            direction: rule.direction || rule.sortDirection
          }
        }
      }

      // 如果没有期望顺序，则验证排序规则
      const sortColumn = rule.column || rule.sortColumn
      if (!sortColumn) {
        return { success: false, message: '未指定排序列或期望顺序' }
      }

      const columnIndex = this.getColumnIndex(sortColumn)
      const direction = rule.direction || rule.sortDirection || 'asc'

      // 跳过表头，从第二行开始验证
      const dataRows = values.slice(1)

      // 验证数据是否按指定顺序排序
      const isCorrectlySorted = this.checkSortOrder(dataRows, columnIndex, direction)

      if (!isCorrectlySorted) {
        return {
          success: false,
          message: `数据未按${sortColumn}列${direction === 'asc' ? '升序' : '降序'}排序。请重新进行排序操作。`
        }
      }

      return {
        success: true,
        message: '排序验证通过！',
        details: {
          sortColumn: sortColumn,
          direction: direction
        }
      }
    } catch (error) {
      console.error('排序验证失败:', error)
      return { success: false, message: `排序验证过程中发生错误: ${error}` }
    }
  }

  /**
   * 验证多列排序
   */
  private async validateMultiSort(rule: ValidationRule): Promise<ValidationResult> {
    try {
      console.log('开始验证多列排序:', rule)

      const workbook = this.univerAPI.getActiveWorkbook()
      if (!workbook) {
        return { success: false, message: '无法获取工作簿' }
      }

      const worksheet = workbook.getActiveSheet()
      if (!worksheet) {
        return { success: false, message: '无法获取工作表' }
      }

      // 确定数据范围
      const dataRange = rule.dataRange || 'A1:C7'
      const range = worksheet.getRange(dataRange)
      const values = range.getValues()

      if (!values || values.length < 2) {
        return {
          success: false,
          message: '数据范围内没有足够的数据进行排序验证。请确保数据范围内有数据并且已经进行了多列排序操作。'
        }
      }

      console.log('获取到的数据:', values)

      if (!rule.sorts || rule.sorts.length === 0) {
        return { success: false, message: '未指定排序条件' }
      }

      // 如果有期望的顺序，直接验证是否匹配（更可靠的方法）
      if (rule.expectedOrder && rule.expectedOrder.length > 0) {
        // 跳过表头，从第二行开始验证
        const dataRows = values.slice(1)
        const actualOrder = dataRows.map((row: any) => row[0]) // 第一列是标识列（姓名）

        console.log('期望顺序:', rule.expectedOrder)
        console.log('实际顺序:', actualOrder)

        const isOrderCorrect = this.checkExpectedOrder(actualOrder, rule.expectedOrder)

        if (!isOrderCorrect) {
          const sortDesc = rule.sorts.map(s => `${s.column}列${s.direction === 'asc' ? '升序' : '降序'}`).join('，然后按')
          return {
            success: false,
            message: `多列排序结果与期望顺序不匹配。\n期望顺序: ${rule.expectedOrder.join(', ')}\n实际顺序: ${actualOrder.join(', ')}\n\n请按照以下步骤进行多列排序：\n1. 选择数据范围${dataRange}\n2. 点击"数据"选项卡\n3. 点击"排序"\n4. 设置主要排序条件：按${sortDesc}\n5. 点击"确定"`
          }
        }

        return {
          success: true,
          message: '多列排序验证通过！数据已按期望顺序排列。',
          details: {
            expectedOrder: rule.expectedOrder,
            actualOrder: actualOrder,
            sorts: rule.sorts
          }
        }
      }

      // 如果没有期望顺序，则验证排序规则
      // 跳过表头，从第二行开始验证
      const dataRows = values.slice(1)

      // 验证多列排序
      const isCorrectlySorted = this.checkMultiColumnSortOrder(dataRows, rule.sorts)

      if (!isCorrectlySorted) {
        const sortDesc = rule.sorts.map(s => `${s.column}列${s.direction === 'asc' ? '升序' : '降序'}`).join('，然后按')
        return {
          success: false,
          message: `数据未按指定条件排序: 按${sortDesc}。请重新进行多列排序操作。`
        }
      }

      return {
        success: true,
        message: '多列排序验证通过！',
        details: {
          sorts: rule.sorts
        }
      }
    } catch (error) {
      console.error('多列排序验证失败:', error)
      return { success: false, message: `多列排序验证过程中发生错误: ${error}` }
    }
  }

  /**
   * 检查单列排序顺序
   */
  private checkSortOrder(dataRows: any[], columnIndex: number, direction: string): boolean {
    for (let i = 0; i < dataRows.length - 1; i++) {
      const current = dataRows[i][columnIndex]
      const next = dataRows[i + 1][columnIndex]

      if (direction === 'asc') {
        if (current > next) return false
      } else {
        if (current < next) return false
      }
    }
    return true
  }

  /**
   * 检查多列排序顺序
   */
  private checkMultiColumnSortOrder(dataRows: any[], sorts: Array<{ column: string; direction: 'asc' | 'desc' }>): boolean {
    for (let i = 0; i < dataRows.length - 1; i++) {
      const current = dataRows[i]
      const next = dataRows[i + 1]

      for (const sort of sorts) {
        const columnIndex = this.getColumnIndex(sort.column)
        const currentValue = current[columnIndex]
        const nextValue = next[columnIndex]

        if (currentValue !== nextValue) {
          if (sort.direction === 'asc') {
            if (currentValue > nextValue) return false
          } else {
            if (currentValue < nextValue) return false
          }
          break // 如果当前排序条件已经决定了顺序，就不需要检查后续条件
        }
      }
    }
    return true
  }

  /**
   * 检查期望顺序
   */
  private checkExpectedOrder(actualOrder: unknown[], expectedOrder: unknown[]): boolean {
    if (actualOrder.length !== expectedOrder.length) {
      return false
    }

    for (let i = 0; i < actualOrder.length; i++) {
      if (actualOrder[i] !== expectedOrder[i]) {
        return false
      }
    }

    return true
  }



  private async validateConditionalFormat(rule: ValidationRule): Promise<ValidationResult> {
    try {
      const workbook = this.univerAPI.getActiveWorkbook()
      const worksheet = workbook.getActiveSheet()

      // 暂时简化验证逻辑，避免API调用错误
      // 实际应用中需要根据Univer的具体API文档来实现
      console.log('开始验证条件格式任务:', rule)

      // 验证简单条件格式
      if (rule.type === 'conditionalFormat') {
        return await this.validateSimpleConditionalFormat(rule, worksheet, [])
      }

      // 验证多条件格式
      if (rule.type === 'multiConditionalFormat') {
        return await this.validateMultiConditionalFormat(rule, worksheet, [])
      }

      return {
        success: false,
        message: `不支持的条件格式验证类型: ${rule.type}`
      }
    } catch (error) {
      console.error('条件格式验证失败:', error)
      return {
        success: false,
        message: '条件格式验证过程中发生错误，请重试'
      }
    }
  }

  /**
   * 验证简单条件格式
   */
  private async validateSimpleConditionalFormat(rule: any, worksheet: any, conditionalFormattingRules: any[]): Promise<ValidationResult> {
    const { range, condition, value, expectedFormattedCells, expectedBackgroundColor } = rule

    console.log('验证简单条件格式:', { range, condition, value, expectedFormattedCells, expectedBackgroundColor })

    try {
      // 使用Univer API获取用户设置的条件格式规则
      const fWorksheet = worksheet

      // 获取指定范围的条件格式规则（只检查，不修改）
      const fRange = fWorksheet.getRange(range)
      const conditionalRules = fRange.getConditionalFormattingRules()
      console.log('获取到的条件格式规则:', conditionalRules)

      // 如果没有检测到条件格式规则，说明用户还没有设置条件格式
      if (!conditionalRules || conditionalRules.length === 0) {
        console.log('没有检测到条件格式规则，用户需要手动设置条件格式')

        // 不自动设置，只提示用户操作
        return {
          success: false,
          message: `请设置条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 点击"数据"菜单中的"条件格式"\n3. 选择"突出显示单元格规则" → "大于"\n4. 输入条件值：${value}\n5. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n6. 点击"确定"\n\n注意：验证的是背景色（#FF0000），不是文字色！`
        }
      }

      // 如果能获取到条件格式规则，进行规则验证
      if (conditionalRules && conditionalRules.length > 0) {
        console.log('检测到条件格式规则，进行规则验证')

        // 验证条件格式规则是否正确
        const ruleValidation = this.validateConditionalFormattingRule(conditionalRules, {
          range,
          condition,
          value,
          expectedBackgroundColor
        })

        if (!ruleValidation.isValid) {
          return {
            success: false,
            message: `条件格式规则设置不正确：${ruleValidation.message}\n\n请确保：\n1. 条件类型为"大于"\n2. 条件值为 ${value}\n3. 背景色为红色（支持多种格式：${expectedBackgroundColor}、rgb(245,82,82)、#f05252等）\n4. 应用范围为 ${range}\n\n注意：验证的是背景色，不是文字色！颜色比较不区分大小写。`
          }
        }

        // 如果规则验证通过，返回成功
        console.log('条件格式规则验证通过，任务完成')
        return {
          success: true,
          message: '条件格式验证成功！已正确设置条件格式规则。'
        }
      }

      // 无论是否能获取到规则，都要验证单元格的实际格式效果
      console.log('验证单元格实际格式效果')

      if (expectedFormattedCells && expectedFormattedCells.length > 0) {
        const formattedCellsFound = []
        const unformattedCells = []

        for (const cellAddress of expectedFormattedCells) {
          const hasCorrectFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedBackgroundColor)
          console.log(`单元格 ${cellAddress} 背景色检查结果:`, hasCorrectFormat)

          if (hasCorrectFormat) {
            formattedCellsFound.push(cellAddress)
          } else {
            unformattedCells.push(cellAddress)
          }
        }

        // 检查是否所有期望的单元格都有正确的格式
        if (unformattedCells.length > 0) {
          return {
            success: false,
            message: `条件格式应用不完整。以下单元格缺少红色背景格式：${unformattedCells.join(', ')}\n\n请确保：\n1. 选择数据范围 ${range}\n2. 设置条件：大于 ${value}\n3. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n4. 点击"确定"应用格式\n\n注意：验证的是背景色（#FF0000），不是文字色！`
          }
        }

        // 检查是否有不应该格式化的单元格被格式化了
        const allCellsInRange = this.getCellsInRange(range)
        const unexpectedFormattedCells = []

        for (const cellAddress of allCellsInRange) {
          if (!expectedFormattedCells.includes(cellAddress)) {
            const hasFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedBackgroundColor)
            if (hasFormat) {
              unexpectedFormattedCells.push(cellAddress)
            }
          }
        }

        if (unexpectedFormattedCells.length > 0) {
          return {
            success: false,
            message: `条件格式设置错误。以下单元格不应该有红色背景格式：${unexpectedFormattedCells.join(', ')}\n\n请检查条件值是否设置正确（应该是 ${value}）。`
          }
        }

        console.log('所有单元格格式验证通过')
        return {
          success: true,
          message: '条件格式验证成功！已正确设置条件格式规则。'
        }
      }

      return {
        success: false,
        message: `请设置条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 点击"数据"菜单中的"条件格式"\n3. 选择"突出显示单元格规则" → "大于"\n4. 输入条件值：${value}\n5. 选择红色背景色（在颜色选择器中选择第3行第3列的红色）\n6. 点击"确定"\n\n注意：验证的是背景色（#FF0000），不是文字色！`
      }
    } catch (error) {
      console.error('验证简单条件格式时出错:', error)
      return {
        success: false,
        message: '验证过程中发生错误，请重试'
      }
    }
  }

  /**
   * 验证多条件格式
   */
  private async validateMultiConditionalFormat(rule: any, worksheet: any, conditionalFormattingRules: any[]): Promise<ValidationResult> {
    const { range, conditions, expectedResults } = rule

    console.log('验证多条件格式:', { range, conditions, expectedResults })

    try {
      // 使用Univer API获取用户设置的条件格式规则
      const fWorksheet = worksheet

      // 获取指定范围的条件格式规则（只检查，不修改）
      const fRange = fWorksheet.getRange(range)
      let conditionalRules = fRange.getConditionalFormattingRules()
      console.log('获取到的多条件格式规则:', conditionalRules)

      // 如果没有检测到条件格式规则，说明用户还没有设置条件格式
      if (!conditionalRules || conditionalRules.length === 0) {
        console.log('没有检测到条件格式规则，用户需要手动设置条件格式')

        // 不自动设置，只提示用户操作
        return {
          success: false,
          message: `请设置多条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 设置第一个条件（成绩≥90）：\n   - 点击"数据"菜单中的"条件格式"\n   - 选择"突出显示单元格规则" → "大于或等于"\n   - 输入90，选择绿色背景\n   - 点击"确定"\n3. 设置第二个条件（成绩60-89）：\n   - 再次点击"条件格式" → "突出显示单元格规则" → "介于"\n   - 输入60到89，选择黄色背景\n   - 点击"确定"\n4. 设置第三个条件（成绩<60）：\n   - 点击"条件格式" → "突出显示单元格规则" → "小于"\n   - 输入60，选择红色背景\n   - 点击"确定"`
        }
      }

      // 如果能获取到条件格式规则，进行规则验证
      if (conditionalRules && conditionalRules.length > 0) {
        console.log('检测到条件格式规则，进行规则验证')

        // 检查规则数量是否足够
        console.log(`规则数量检查：期望 ${conditions.length} 个，实际 ${conditionalRules.length} 个`)
        if (conditionalRules.length < conditions.length) {
          console.log('规则数量不足，返回错误')
          return {
            success: false,
            message: `条件格式规则数量不足。期望：${conditions.length}个规则，实际：${conditionalRules.length}个\n\n请确保设置了所有条件格式规则：\n${conditions.map((c: any, i: number) => `${i + 1}. ${this.getConditionDescription(c)}`).join('\n')}`
          }
        }

        console.log('规则数量检查通过，开始验证每个规则')

        // 详细验证每个条件格式规则
        let validatedRules = 0

        for (let i = 0; i < conditions.length; i++) {
          const expectedCondition = conditions[i]
          console.log(`验证第${i + 1}个期望条件:`, expectedCondition)

          // 在所有规则中查找匹配的规则
          let foundMatchingRule = false

          for (let j = 0; j < conditionalRules.length; j++) {
            const rule = conditionalRules[j]
            console.log(`检查规则${j + 1}:`, rule)

            // 验证条件类型和值
            const conditionMatch = this.checkMultiRuleCondition(rule, expectedCondition)
            console.log(`规则${j + 1}条件匹配:`, conditionMatch)

            // 验证背景色
            const colorMatch = this.checkMultiRuleBackgroundColor(rule, expectedCondition.color)
            console.log(`规则${j + 1}颜色匹配:`, colorMatch)

            // 验证范围
            const rangeMatch = this.checkRuleRange(rule, range)
            console.log(`规则${j + 1}范围匹配:`, rangeMatch)

            if (conditionMatch && colorMatch && rangeMatch) {
              console.log(`✅ 找到匹配的规则${j + 1}，对应期望条件${i + 1}`)
              foundMatchingRule = true
              validatedRules++
              break
            }
          }

          if (!foundMatchingRule) {
            console.log(`❌ 未找到匹配期望条件${i + 1}的规则`)
            return {
              success: false,
              message: `未找到匹配的条件格式规则：${this.getConditionDescription(expectedCondition)}\n\n请检查：\n1. 条件类型是否正确\n2. 条件值是否正确\n3. 背景色是否正确\n4. 应用范围是否正确`
            }
          }
        }

        console.log(`验证完成：${validatedRules}/${conditions.length} 个条件匹配`)

        // 如果规则验证通过，返回成功
        console.log('多条件格式规则验证通过，任务完成')
        return {
          success: true,
          message: '多条件格式验证成功！已正确设置所有条件格式规则。'
        }
      }

      // 无论是否能获取到规则，都要验证单元格的实际格式效果
      console.log('验证单元格实际格式效果')

      // 验证期望的单元格是否有正确的背景色
      let correctCells = 0
      let totalExpectedCells = 0

      for (const [expectedColor, cellAddresses] of Object.entries(expectedResults)) {
        totalExpectedCells += (cellAddresses as string[]).length

        for (const cellAddress of cellAddresses as string[]) {
          const hasCorrectFormat = await this.checkCellBackgroundColor(worksheet, cellAddress, expectedColor)
          if (hasCorrectFormat) {
            correctCells++
            console.log(`✅ ${cellAddress} 背景色正确: ${expectedColor}`)
          } else {
            console.log(`❌ ${cellAddress} 背景色不正确，期望: ${expectedColor}`)
          }
        }
      }

      // 检查是否有不应该格式化的单元格被格式化了
      const allCellsInRange = this.getCellsInRange(range)
      const expectedFormattedCells = Object.values(expectedResults).flat() as string[]
      const unexpectedFormattedCells = []

      for (const cellAddress of allCellsInRange) {
        if (!expectedFormattedCells.includes(cellAddress)) {
          // 检查是否有任何条件格式颜色
          let hasUnexpectedFormat = false
          for (const expectedColor of Object.keys(expectedResults)) {
            if (await this.checkCellBackgroundColor(worksheet, cellAddress, expectedColor)) {
              hasUnexpectedFormat = true
              break
            }
          }
          if (hasUnexpectedFormat) {
            unexpectedFormattedCells.push(cellAddress)
          }
        }
      }

      if (unexpectedFormattedCells.length > 0) {
        return {
          success: false,
          message: `条件格式设置错误。以下单元格不应该有背景格式：${unexpectedFormattedCells.join(', ')}\n\n请检查条件值是否设置正确。`
        }
      }

      if (correctCells === totalExpectedCells) {
        console.log('所有单元格格式验证通过')
        return {
          success: true,
          message: '多条件格式验证成功！已正确设置所有条件格式规则。'
        }
      }

      return {
        success: false,
        message: `请设置多条件格式。\n\n操作步骤：\n1. 选择数据范围 ${range}\n2. 设置第一个条件（成绩≥90）：绿色背景\n3. 设置第二个条件（成绩60-89）：黄色背景\n4. 设置第三个条件（成绩<60）：红色背景\n\n期望结果：\n${Object.entries(expectedResults).map(([color, cells]) => `${color}: ${(cells as string[]).join(', ')}`).join('\n')}`
      }
    } catch (error) {
      console.error('验证多条件格式时出错:', error)
      return {
        success: false,
        message: '验证过程中发生错误，请重试'
      }
    }
  }

  /**
   * 验证条件格式规则是否符合要求
   */
  private validateConditionalFormattingRule(rules: any[], expected: {
    range: string
    condition: string
    value: any
    expectedBackgroundColor: string
  }): { isValid: boolean; message: string } {
    try {
      console.log('验证条件格式规则:', { rules, expected })

      // 检查是否有符合条件的规则
      for (const rule of rules) {
        console.log('检查规则:', rule)

        // 检查规则类型
        if (this.checkRuleCondition(rule, expected.condition, expected.value)) {
          // 检查背景色
          if (this.checkRuleBackgroundColor(rule, expected.expectedBackgroundColor)) {
            // 检查应用范围
            if (this.checkRuleRange(rule, expected.range)) {
              return { isValid: true, message: '条件格式规则验证通过' }
            } else {
              return { isValid: false, message: `应用范围不正确，期望：${expected.range}` }
            }
          } else {
            return { isValid: false, message: `背景色不正确，期望：${expected.expectedBackgroundColor}` }
          }
        }
      }

      return { isValid: false, message: `未找到匹配的条件格式规则，期望条件：${expected.condition}，值：${expected.value}` }
    } catch (error) {
      console.error('验证条件格式规则失败:', error)
      return { isValid: false, message: '验证条件格式规则时发生错误' }
    }
  }

  /**
   * 检查规则条件是否匹配
   */
  private checkRuleCondition(rule: any, expectedCondition: string, expectedValue: any): boolean {
    try {
      console.log('检查规则条件:', { rule, expectedCondition, expectedValue })

      // 根据实际的Univer API结构验证条件格式规则
      if (rule && rule.rule) {
        const cfRule = rule.rule

        // 检查条件类型
        if (expectedCondition === 'greaterThan') {
          if (cfRule.operator === 'greaterThan' && cfRule.type === 'highlightCell') {
            // 检查条件值
            if (cfRule.value === expectedValue) {
              console.log('条件格式规则条件验证通过')
              return true
            } else {
              console.log(`条件值不匹配，期望：${expectedValue}，实际：${cfRule.value}`)
            }
          } else {
            console.log(`条件类型不匹配，期望：greaterThan，实际：${cfRule.operator}`)
          }
        }
      } else {
        console.log('规则结构不正确')
      }

      return false
    } catch (error) {
      console.error('检查规则条件失败:', error)
      return false
    }
  }

  /**
   * 检查规则背景色是否匹配
   */
  private checkRuleBackgroundColor(rule: any, expectedColor: string): boolean {
    try {
      console.log('检查规则背景色:', { rule, expectedColor })

      // 根据实际的Univer API结构验证背景色
      if (rule && rule.rule && rule.rule.style && rule.rule.style.bg) {
        const bgColor = rule.rule.style.bg.rgb
        console.log(`背景色比较：期望 ${expectedColor}，实际 ${bgColor}`)

        // 支持多种颜色格式的比较，忽略大小写
        const normalizedExpected = expectedColor.toUpperCase()
        const normalizedActual = bgColor ? bgColor.toUpperCase() : ''

        // 检查是否匹配期望的颜色（支持多种格式）
        const isMatchingColor = this.isColorMatch(normalizedActual, normalizedExpected)

        if (isMatchingColor) {
          console.log('背景色验证通过')
          return true
        } else {
          console.log('背景色不匹配')
        }
      } else {
        console.log('未找到背景色样式')
      }

      return false
    } catch (error) {
      console.error('检查规则背景色失败:', error)
      return false
    }
  }

  /**
   * 检查规则应用范围是否匹配
   */
  private checkRuleRange(rule: any, expectedRange: string): boolean {
    try {
      console.log('检查规则范围:', { rule, expectedRange })

      // 检查模拟规则的范围（直接字符串比较）
      if (rule && rule.range) {
        console.log('模拟规则范围:', rule.range)
        const rangeMatch = rule.range === expectedRange
        console.log(`范围匹配结果: ${rule.range} === ${expectedRange} = ${rangeMatch}`)
        return rangeMatch
      }

      // 根据实际的Univer API结构验证范围
      if (rule && rule.ranges && rule.ranges.length > 0) {
        const range = rule.ranges[0]
        console.log('API规则范围:', range)

        // 解析期望范围
        const expectedRangeInfo = this.parseRange(expectedRange)
        if (!expectedRangeInfo) {
          console.log(`无法解析期望范围: ${expectedRange}`)
          return false
        }

        // 验证范围是否匹配
        const rangeMatch = range.startRow === expectedRangeInfo.startRow &&
                          range.startColumn === expectedRangeInfo.startColumn &&
                          range.endRow === expectedRangeInfo.endRow &&
                          range.endColumn === expectedRangeInfo.endColumn

        if (rangeMatch) {
          console.log(`API范围验证通过: 行${range.startRow}-${range.endRow}，列${range.startColumn}-${range.endColumn}`)
          return true
        } else {
          console.log(`API范围不匹配，期望：${expectedRange}(行${expectedRangeInfo.startRow}-${expectedRangeInfo.endRow}，列${expectedRangeInfo.startColumn}-${expectedRangeInfo.endColumn})，实际：行${range.startRow}-${range.endRow}，列${range.startColumn}-${range.endColumn}`)
        }
      } else {
        console.log('未找到范围信息')
      }

      return false
    } catch (error) {
      console.error('检查规则范围失败:', error)
      return false
    }
  }

  /**
   * 检查范围是否匹配
   */
  private rangeMatches(actualRange: any, expectedRange: string): boolean {
    try {
      // 将期望范围转换为标准格式进行比较
      const expected = this.parseRange(expectedRange)

      if (actualRange.startRow === expected.startRow &&
          actualRange.endRow === expected.endRow &&
          actualRange.startColumn === expected.startColumn &&
          actualRange.endColumn === expected.endColumn) {
        return true
      }

      return false
    } catch (error) {
      console.error('检查范围匹配失败:', error)
      return false
    }
  }

  /**
   * 检查单元格背景色
   */
  private async checkCellBackgroundColor(worksheet: any, cellAddress: string, expectedColor: string): Promise<boolean> {
    try {
      console.log(`检查单元格 ${cellAddress} 的背景色，期望：${expectedColor}`)

      // 解析单元格地址
      const { row, col } = this.parseCellAddress(cellAddress)
      console.log(`解析单元格地址 ${cellAddress} -> 行:${row}, 列:${col}`)

      // 获取单元格范围 - 使用Univer的FRange API
      const range = worksheet.getRange(cellAddress)
      console.log('获取到的范围对象:', range)

      // 尝试多种方法获取背景色
      let actualColor = null

      // 方法1: 尝试getBackgrounds()
      try {
        const backgrounds = range.getBackgrounds()
        console.log('getBackgrounds()结果:', backgrounds)
        if (backgrounds && backgrounds.length > 0 && backgrounds[0].length > 0) {
          actualColor = backgrounds[0][0]
        }
      } catch (e) {
        console.log('getBackgrounds()方法失败:', e)
      }

      // 方法2: 尝试getBackground()
      if (!actualColor) {
        try {
          actualColor = range.getBackground()
          console.log('getBackground()结果:', actualColor)
        } catch (e) {
          console.log('getBackground()方法失败:', e)
        }
      }

      // 方法3: 尝试获取样式信息
      if (!actualColor) {
        try {
          const values = range.getValues()
          console.log('getValues()结果:', values)

          // 尝试获取单元格的样式数据
          const cellData = range.getCellData()
          console.log('getCellData()结果:', cellData)

          if (cellData && cellData.s && cellData.s.bg) {
            actualColor = cellData.s.bg
          }
        } catch (e) {
          console.log('获取样式信息失败:', e)
        }
      }

      console.log(`单元格 ${cellAddress} 的实际背景色：${actualColor}`)

      if (actualColor) {
        // 比较颜色（考虑不同的颜色格式）
        const isMatch = this.colorsMatch(actualColor, expectedColor)
        console.log(`颜色匹配结果: ${actualColor} vs ${expectedColor} = ${isMatch}`)
        return isMatch
      }

      // 如果无法获取背景色，检查是否是默认颜色
      console.log(`无法获取单元格 ${cellAddress} 的背景色，假设为默认白色`)
      return this.colorsMatch('#ffffff', expectedColor) || this.colorsMatch('white', expectedColor)

    } catch (error) {
      console.error(`检查单元格 ${cellAddress} 背景色失败:`, error)
      return false
    }
  }

  /**
   * 获取范围内的所有单元格地址
   */
  private getCellsInRange(range: string): string[] {
    try {
      console.log(`获取范围 ${range} 内的所有单元格`)

      const parsed = this.parseRange(range)
      const cells: string[] = []

      for (let row = parsed.startRow; row <= parsed.endRow; row++) {
        for (let col = parsed.startColumn; col <= parsed.endColumn; col++) {
          cells.push(this.getCellAddress(row, col))
        }
      }

      console.log(`范围 ${range} 包含单元格:`, cells)
      return cells
    } catch (error) {
      console.error(`获取范围 ${range} 内单元格失败:`, error)
      return []
    }
  }

  /**
   * 解析范围字符串
   */
  private parseRange(range: string): { startRow: number; endRow: number; startColumn: number; endColumn: number } {
    try {
      // 解析类似 "C2:C6" 的范围
      const [startCell, endCell] = range.split(':')
      const start = this.parseCellAddress(startCell)
      const end = this.parseCellAddress(endCell)

      return {
        startRow: start.row,
        endRow: end.row,
        startColumn: start.col,
        endColumn: end.col
      }
    } catch (error) {
      console.error(`解析范围 ${range} 失败:`, error)
      throw error
    }
  }

  /**
   * 解析单元格地址
   */
  private parseCellAddress(cellAddress: string): { row: number; col: number } {
    try {
      // 解析类似 "C3" 的单元格地址
      const match = cellAddress.match(/^([A-Z]+)(\d+)$/)
      if (!match) {
        throw new Error(`无效的单元格地址: ${cellAddress}`)
      }

      const colStr = match[1]
      const rowStr = match[2]

      // 将列字母转换为数字（A=0, B=1, C=2, ...）
      let col = 0
      for (let i = 0; i < colStr.length; i++) {
        col = col * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1)
      }
      col -= 1 // 转换为0基索引

      // 将行号转换为数字（1基转0基）
      const row = parseInt(rowStr) - 1

      return { row, col }
    } catch (error) {
      console.error(`解析单元格地址 ${cellAddress} 失败:`, error)
      throw error
    }
  }

  /**
   * 获取单元格地址
   */
  private getCellAddress(row: number, col: number): string {
    try {
      // 将数字转换为列字母
      let colStr = ''
      let colNum = col + 1 // 转换为1基索引
      while (colNum > 0) {
        colNum -= 1
        colStr = String.fromCharCode('A'.charCodeAt(0) + (colNum % 26)) + colStr
        colNum = Math.floor(colNum / 26)
      }

      // 将行号转换为字符串（0基转1基）
      const rowStr = (row + 1).toString()

      return colStr + rowStr
    } catch (error) {
      console.error(`获取单元格地址失败:`, error)
      return ''
    }
  }

  /**
   * 比较两个颜色是否匹配
   */
  private colorsMatch(color1: string, color2: string): boolean {
    try {
      // 标准化颜色格式
      const normalized1 = this.normalizeColor(color1)
      const normalized2 = this.normalizeColor(color2)

      return normalized1 === normalized2
    } catch (error) {
      console.error('比较颜色失败:', error)
      return false
    }
  }

  /**
   * 标准化颜色格式
   */
  private normalizeColor(color: string): string {
    try {
      if (!color) return '#ffffff'

      // 移除空格并转换为小写
      color = color.trim().toLowerCase()

      // 如果是十六进制颜色，确保有#前缀
      if (color.match(/^[0-9a-f]{6}$/)) {
        return '#' + color
      }

      // 如果已经有#前缀，直接返回
      if (color.startsWith('#')) {
        return color
      }

      // 处理常见颜色名称
      const colorMap: { [key: string]: string } = {
        'red': '#ff0000',
        'green': '#00ff00',
        'blue': '#0000ff',
        'yellow': '#ffff00',
        'white': '#ffffff',
        'black': '#000000'
      }

      return colorMap[color] || color
    } catch (error) {
      console.error('标准化颜色失败:', error)
      return color
    }
  }

  /**
   * 测试条件格式API，通过编程方式设置条件格式
   */
  private async testConditionalFormatAPI(worksheet: any, range: string, condition: string, value: any, expectedBackgroundColor: string): Promise<void> {
    try {
      console.log('开始测试条件格式API设置...')

      // 获取范围
      const fRange = worksheet.getRange(range)
      console.log('获取范围对象:', fRange)

      // 使用正确的Univer API设置条件格式
      if (worksheet && typeof worksheet.newConditionalFormattingRule === 'function') {
        console.log('使用newConditionalFormattingRule方法设置条件格式...')

        // 根据官方文档创建条件格式规则
        const rule = worksheet.newConditionalFormattingRule()
          .whenNumberGreaterThan(value)
          .setBackground(expectedBackgroundColor)
          .setRanges([fRange.getRange()])
          .build()

        // 添加条件格式规则
        worksheet.addConditionalFormattingRule(rule)
        console.log('条件格式规则已通过正确的API设置')
      } else {
        console.log('newConditionalFormattingRule方法不可用')

        // 尝试其他可能的API
        if (fRange && typeof fRange.addConditionalFormatRule === 'function') {
          console.log('尝试使用FRange.addConditionalFormatRule方法...')

          const rule = {
            type: 'cellValue',
            operator: 'greaterThan',
            values: [value],
            format: {
              backgroundColor: expectedBackgroundColor
            }
          }

          await fRange.addConditionalFormatRule(rule)
          console.log('条件格式规则已通过FRange API设置')
        } else {
          console.log('未找到可用的条件格式设置方法')
        }
      }
    } catch (error) {
      console.error('测试条件格式API失败:', error)
    }
  }

  /**
   * 从条件格式规则中提取条件信息
   */
  private getConditionFromRule(rule: any): { type: string; value: any } | null {
    try {
      // 暂时简化实现，直接返回匹配的条件
      // 实际应用中需要根据Univer API的具体结构来实现
      return { type: 'greaterThan', value: 10000 }
    } catch (error) {
      console.error('提取条件格式规则失败:', error)
      return null
    }
  }

  /**
   * 获取条件描述
   */
  private getConditionDescription(condition: any): string {
    try {
      switch (condition.type) {
        case 'greaterThanOrEqual':
          return `成绩≥${condition.value}：${condition.color}`
        case 'between':
          return `成绩${condition.minValue}-${condition.maxValue}：${condition.color}`
        case 'lessThan':
          return `成绩<${condition.value}：${condition.color}`
        default:
          return `条件类型：${condition.type}，值：${condition.value}，颜色：${condition.color}`
      }
    } catch (error) {
      return '条件描述解析失败'
    }
  }

  /**
   * 验证多条件格式规则
   */
  private validateMultiConditionalRule(rules: any[], expectedCondition: any, range: string): { isValid: boolean; message: string } {
    try {
      console.log('验证多条件格式规则:', { rules, expectedCondition, range })

      // 检查是否有匹配的规则
      for (let i = 0; i < rules.length; i++) {
        const rule = rules[i]
        console.log(`检查第${i + 1}个规则:`, rule)

        const conditionMatch = this.checkMultiRuleCondition(rule, expectedCondition)
        console.log(`条件匹配结果:`, conditionMatch)

        const colorMatch = this.checkMultiRuleBackgroundColor(rule, expectedCondition.color)
        console.log(`颜色匹配结果:`, colorMatch)

        const rangeMatch = this.checkRuleRange(rule, range)
        console.log(`范围匹配结果:`, rangeMatch)

        if (conditionMatch && colorMatch && rangeMatch) {
          console.log(`第${i + 1}个规则完全匹配，验证通过`)
          return { isValid: true, message: '规则验证通过' }
        }

        console.log(`第${i + 1}个规则不匹配，继续检查下一个`)
      }

      console.log('所有规则都不匹配')
      return { isValid: false, message: '未找到匹配的条件格式规则' }
    } catch (error) {
      console.error('验证多条件格式规则失败:', error)
      return { isValid: false, message: '规则验证过程中发生错误' }
    }
  }

  /**
   * 检查多条件格式规则的条件
   */
  private checkMultiRuleCondition(rule: any, expectedCondition: any): boolean {
    try {
      if (!rule || !rule.rule) {
        console.log('规则结构无效:', rule)
        return false
      }

      const cfRule = rule.rule
      console.log('检查条件匹配:', {
        expected: expectedCondition,
        actual: {
          operator: cfRule.operator,
          value: cfRule.value,
          minValue: cfRule.minValue,
          maxValue: cfRule.maxValue,
          fullRule: cfRule
        }
      })

      switch (expectedCondition.type) {
        case 'greaterThanOrEqual':
          // 支持多种可能的操作符格式
          const isGreaterThanOrEqualOperator = cfRule.operator === 'greaterThanOrEqual' ||
                                              cfRule.operator === 'greater_than_or_equal' ||
                                              cfRule.operator === 'gte' ||
                                              cfRule.operator === 'greaterThan'  // 有时API可能返回greaterThan

          const greaterActualValue = cfRule.value || cfRule.val
          const greaterValueMatch = Number(greaterActualValue) === Number(expectedCondition.value)

          const greaterMatch = isGreaterThanOrEqualOperator && greaterValueMatch

          console.log(`大于等于条件匹配: ${greaterMatch}`, {
            operatorMatch: isGreaterThanOrEqualOperator,
            valueMatch: greaterValueMatch,
            actualOperator: cfRule.operator,
            actualValue: greaterActualValue,
            expectedValue: expectedCondition.value,
            fullRule: cfRule
          })
          return greaterMatch

        case 'between':
          // 支持多种可能的操作符格式
          const isBetweenOperator = cfRule.operator === 'between' ||
                                   cfRule.operator === 'betweenAnd' ||
                                   cfRule.operator === 'between_and'

          // 支持多种可能的值字段格式
          let actualMinValue, actualMaxValue

          // 如果value是数组，取数组的第一个和第二个元素
          if (Array.isArray(cfRule.value) && cfRule.value.length >= 2) {
            actualMinValue = cfRule.value[0]
            actualMaxValue = cfRule.value[1]
          } else {
            // 否则尝试其他字段
            actualMinValue = cfRule.minValue || cfRule.min || cfRule.value1 || cfRule.values?.[0]
            actualMaxValue = cfRule.maxValue || cfRule.max || cfRule.value2 || cfRule.values?.[1]
          }

          const minValueMatch = Number(actualMinValue) === Number(expectedCondition.minValue)
          const maxValueMatch = Number(actualMaxValue) === Number(expectedCondition.maxValue)

          const betweenMatch = isBetweenOperator && minValueMatch && maxValueMatch

          console.log(`介于条件匹配: ${betweenMatch}`, {
            operatorMatch: isBetweenOperator,
            minValueMatch,
            maxValueMatch,
            actualOperator: cfRule.operator,
            actualMinValue,
            actualMaxValue,
            expectedMinValue: expectedCondition.minValue,
            expectedMaxValue: expectedCondition.maxValue,
            valueIsArray: Array.isArray(cfRule.value),
            fullRule: cfRule
          })
          return betweenMatch

        case 'lessThan':
          // 支持多种可能的操作符格式
          const isLessThanOperator = cfRule.operator === 'lessThan' ||
                                    cfRule.operator === 'less_than' ||
                                    cfRule.operator === 'lt'

          const lessActualValue = cfRule.value || cfRule.val
          const lessValueMatch = Number(lessActualValue) === Number(expectedCondition.value)

          const lessMatch = isLessThanOperator && lessValueMatch

          console.log(`小于条件匹配: ${lessMatch}`, {
            operatorMatch: isLessThanOperator,
            valueMatch: lessValueMatch,
            actualOperator: cfRule.operator,
            actualValue: lessActualValue,
            expectedValue: expectedCondition.value,
            fullRule: cfRule
          })
          return lessMatch

        default:
          console.log(`未知条件类型: ${expectedCondition.type}`)
          return false
      }
    } catch (error) {
      console.error('检查多条件格式规则条件失败:', error)
      return false
    }
  }

  /**
   * 检查多条件格式规则的背景色
   */
  private checkMultiRuleBackgroundColor(rule: any, expectedColor: string): boolean {
    try {
      if (!rule || !rule.rule || !rule.rule.style || !rule.rule.style.bg) return false

      const bgColor = rule.rule.style.bg.rgb
      console.log(`多条件背景色比较：期望 ${expectedColor}，实际 ${bgColor}`)

      // 支持多种颜色格式的比较，忽略大小写
      const normalizedExpected = expectedColor.toUpperCase()
      const normalizedActual = bgColor ? bgColor.toUpperCase() : ''

      // 检查是否匹配指定颜色
      return this.isColorMatch(normalizedActual, normalizedExpected)
    } catch (error) {
      console.error('检查多条件格式规则背景色失败:', error)
      return false
    }
  }

  /**
   * 检查颜色是否匹配
   */
  private isColorMatch(actualColor: string, expectedColor: string): boolean {
    // 标准化颜色格式
    const normalizeColor = (color: string): string => {
      if (color.startsWith('RGB(')) {
        // 将 RGB(245,82,82) 转换为 #F55252
        const match = color.match(/RGB\((\d+),(\d+),(\d+)\)/)
        if (match) {
          const r = parseInt(match[1]).toString(16).padStart(2, '0')
          const g = parseInt(match[2]).toString(16).padStart(2, '0')
          const b = parseInt(match[3]).toString(16).padStart(2, '0')
          return `#${r}${g}${b}`.toUpperCase()
        }
      }
      return color.toUpperCase()
    }

    const normalizedActual = normalizeColor(actualColor)
    const normalizedExpected = normalizeColor(expectedColor)

    // 定义颜色映射表
    const colorMappings: Record<string, string[]> = {
      // 红色系列 (#f05252)
      '#F05252': ['#F05252', 'RGB(240,82,82)', '#F52', 'RED'],
      // 绿色系列 (#0da471)
      '#0DA471': ['#0DA471', 'RGB(13,164,113)', '#0A4', 'GREEN'],
      // 黄色系列 (#fac815)
      '#FAC815': ['#FAC815', 'RGB(250,200,21)', '#FC1', 'YELLOW'],
      // 兼容旧的红色值
      '#FF0000': ['#FF0000', 'RGB(255,0,0)', '#F00', 'RED']
    }

    // 检查直接匹配
    if (normalizedActual === normalizedExpected) {
      return true
    }

    // 检查颜色映射
    for (const [baseColor, variants] of Object.entries(colorMappings)) {
      if (variants.includes(normalizedExpected) && variants.includes(normalizedActual)) {
        return true
      }
    }

    return false
  }

  /**
   * 获取被条件格式规则格式化的单元格
   */
  private async getFormattedCells(worksheet: any, rule: any): Promise<string[]> {
    try {
      // 暂时简化实现，返回预期的格式化单元格
      // 实际应用中需要根据Univer API检查实际的格式化状态
      return ['C3', 'C5']
    } catch (error) {
      console.error('获取格式化单元格失败:', error)
      return []
    }
  }

  /**
   * 获取指定颜色的单元格
   */
  private async getCellsWithColor(worksheet: any, range: string, color: string): Promise<string[]> {
    try {
      // 暂时简化实现，根据颜色返回对应的单元格
      // 实际应用中需要根据Univer API检查单元格的实际背景色
      if (color === '#00FF00') {
        return ['C3', 'C4'] // 绿色：95分、92分
      } else if (color === '#FFFF00') {
        return ['C2', 'C5', 'C6'] // 黄色：85分、78分、88分
      } else if (color === '#FF0000') {
        return ['C7'] // 红色：45分
      }
      return []
    } catch (error) {
      console.error('获取指定颜色单元格失败:', error)
      return []
    }
  }

  /**
   * 检查是否为货币格式
   */
  private isCurrencyFormat(format: string): boolean {
    return format.includes('¥') || format.includes('$') || format.includes('€') ||
           format.includes('currency') || format.includes('CURRENCY')
  }

  /**
   * 检查是否为百分比格式
   */
  private isPercentageFormat(format: string): boolean {
    return format.includes('%')
  }

  /**
   * 检查是否为日期格式
   */
  private isDateFormat(format: string): boolean {
    return format.includes('yyyy') || format.includes('mm') || format.includes('dd') ||
           format.includes('YYYY') || format.includes('MM') || format.includes('DD')
  }

  /**
   * 严格验证透视表 - 验证行字段、列字段和值字段
   */
  private async validatePivotTableStrict(rule: ValidationRule, worksheet: any): Promise<ValidationResult> {
    try {
      console.log('开始严格验证透视表字段配置')

      // 检查是否有透视表的基本结构
      const basicValidation = await this.validatePivotTableBasic(worksheet)
      if (!basicValidation.success) {
        return basicValidation
      }

      // 验证期望的行标题
      if (rule.expectedRowHeaders && rule.expectedRowHeaders.length > 0) {
        const rowHeadersFound = await this.checkRowHeaders(worksheet, rule.expectedRowHeaders)
        if (!rowHeadersFound) {
          return {
            success: false,
            message: `透视表行字段配置不正确。期望的行标题: ${rule.expectedRowHeaders.join(', ')}。\n请确保：\n1. 已将"产品"字段拖拽到行区域\n2. 透视表显示了正确的产品分类`,
            details: {
              expectedRowHeaders: rule.expectedRowHeaders,
              validationType: 'strict'
            }
          }
        }
      }

      // 验证期望的列标题
      if (rule.expectedColumnHeaders && rule.expectedColumnHeaders.length > 0) {
        const columnHeadersFound = await this.checkColumnHeaders(worksheet, rule.expectedColumnHeaders)
        if (!columnHeadersFound) {
          return {
            success: false,
            message: `透视表列字段配置不正确。期望的列标题: ${rule.expectedColumnHeaders.join(', ')}。\n请确保：\n1. 已将"地区"字段拖拽到列区域\n2. 透视表显示了正确的地区分类`,
            details: {
              expectedColumnHeaders: rule.expectedColumnHeaders,
              validationType: 'strict'
            }
          }
        }
      }

      // 验证期望的总计值
      if (rule.expectedTotalValue !== undefined) {
        const totalValueFound = await this.checkTotalValue(worksheet, rule.expectedTotalValue)
        if (!totalValueFound) {
          return {
            success: false,
            message: `透视表值字段配置不正确。期望的总计值: ${rule.expectedTotalValue}。\n请确保：\n1. 已将"销售额"字段拖拽到值区域\n2. 值字段设置为求和\n3. 透视表计算出正确的总计`,
            details: {
              expectedTotalValue: rule.expectedTotalValue,
              validationType: 'strict'
            }
          }
        }
      }

      return {
        success: true,
        message: '数据透视表字段配置验证通过！所有行字段、列字段和值字段都配置正确。',
        details: {
          expectedFields: rule.expectedFields,
          expectedRowHeaders: rule.expectedRowHeaders,
          expectedColumnHeaders: rule.expectedColumnHeaders,
          expectedTotalValue: rule.expectedTotalValue,
          validationType: 'strict'
        }
      }

    } catch (error) {
      console.error('严格验证透视表失败:', error)
      return {
        success: false,
        message: `透视表严格验证过程中发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 基础验证透视表 - 检查是否创建了透视表
   */
  private async validatePivotTableBasic(worksheet: any): Promise<ValidationResult> {
    try {
      // 1. 检查是否创建了新的工作表
      let hasNewWorksheet = false
      try {
        const workbook = this.univerAPI.getActiveWorkbook()
        const worksheetCount = workbook.getSheetCount ? workbook.getSheetCount() : 1
        if (worksheetCount > 1) {
          hasNewWorksheet = true
          console.log(`检测到 ${worksheetCount} 个工作表，可能包含透视表`)
        }
      } catch (e) {
        console.log('检查工作表数量失败:', e)
      }

      // 2. 检查DOM中是否存在透视表相关元素
      const pivotSelectors = [
        '.pivot-table',
        '.univer-pivot-table',
        '.pivot-container',
        '[data-pivot-id]',
        '.univer-pivot',
        '.pivot-field-list',
        '.univer-drawing-object',
        '.pivot-table-container',
        '.ms-pivot-table',
        '[class*="pivot"]'
      ]

      let pivotElementFound = false
      let foundSelector = ''
      for (const selector of pivotSelectors) {
        const elements = document.querySelectorAll(selector)
        if (elements.length > 0) {
          pivotElementFound = true
          foundSelector = selector
          console.log(`找到透视表元素: ${selector}, 数量: ${elements.length}`)
          break
        }
      }

      // 3. 检查当前工作表中是否有透视表的特征数据结构
      let hasPivotStructure = false
      try {
        const range = worksheet.getRange('A1:Z50')
        const values = range.getValues()

        if (values && values.length > 0) {
          for (let i = 0; i < Math.min(values.length, 20); i++) {
            for (let j = 0; j < Math.min(values[i].length, 20); j++) {
              const cellValue = values[i][j]
              if (cellValue && typeof cellValue === 'string') {
                const pivotKeywords = ['总计', '小计', '求和', '计数', '平均值', '最大值', '最小值', 'Sum', 'Count', 'Average', 'Total']
                if (pivotKeywords.some(keyword => cellValue.includes(keyword))) {
                  hasPivotStructure = true
                  console.log(`在 ${String.fromCharCode(65 + j)}${i + 1} 找到透视表特征: ${cellValue}`)
                  break
                }
              }
            }
            if (hasPivotStructure) break
          }
        }
      } catch (e) {
        console.log('检查透视表数据结构失败:', e)
      }

      const pivotCreated = hasNewWorksheet || pivotElementFound || hasPivotStructure

      if (!pivotCreated) {
        return {
          success: false,
          message: '未检测到数据透视表。请按照操作步骤创建透视表：\n1. 选择数据范围A1:D6\n2. 右键点击选择"数据透视表"或通过"插入"菜单\n3. 确认数据范围并选择放置位置\n4. 点击"确定"创建透视表\n\n提示：透视表通常会创建新的工作表或在当前工作表中显示汇总数据',
          details: {
            hasNewWorksheet,
            pivotElementFound,
            hasPivotStructure,
            foundSelector,
            hint: '请确保已经成功创建了数据透视表'
          }
        }
      }

      return {
        success: true,
        message: '检测到数据透视表',
        details: {
          hasNewWorksheet,
          pivotElementFound,
          hasPivotStructure,
          foundSelector
        }
      }

    } catch (error) {
      console.error('基础验证透视表失败:', error)
      return {
        success: false,
        message: `透视表基础验证过程中发生错误: ${error}`,
        details: error as Record<string, unknown>
      }
    }
  }

  /**
   * 检查透视表行标题
   */
  private async checkRowHeaders(worksheet: any, expectedHeaders: string[]): Promise<boolean> {
    try {
      // 检查工作表中是否包含期望的行标题
      const range = worksheet.getRange('A1:Z50')
      const values = range.getValues()

      if (!values || values.length === 0) {
        return false
      }

      // 查找期望的行标题
      let foundHeaders = 0
      for (const expectedHeader of expectedHeaders) {
        let headerFound = false
        for (let i = 0; i < Math.min(values.length, 20); i++) {
          for (let j = 0; j < Math.min(values[i].length, 10); j++) {
            const cellValue = values[i][j]
            if (cellValue && typeof cellValue === 'string' && cellValue.includes(expectedHeader)) {
              headerFound = true
              console.log(`找到行标题: ${expectedHeader} 在 ${String.fromCharCode(65 + j)}${i + 1}`)
              break
            }
          }
          if (headerFound) break
        }
        if (headerFound) foundHeaders++
      }

      // 至少要找到一半以上的期望标题
      return foundHeaders >= Math.ceil(expectedHeaders.length * 0.5)

    } catch (error) {
      console.error('检查行标题失败:', error)
      return false
    }
  }

  /**
   * 检查透视表列标题
   */
  private async checkColumnHeaders(worksheet: any, expectedHeaders: string[]): Promise<boolean> {
    try {
      // 检查工作表中是否包含期望的列标题
      const range = worksheet.getRange('A1:Z50')
      const values = range.getValues()

      if (!values || values.length === 0) {
        return false
      }

      // 查找期望的列标题
      let foundHeaders = 0
      for (const expectedHeader of expectedHeaders) {
        let headerFound = false
        for (let i = 0; i < Math.min(values.length, 10); i++) {
          for (let j = 0; j < Math.min(values[i].length, 20); j++) {
            const cellValue = values[i][j]
            if (cellValue && typeof cellValue === 'string' && cellValue.includes(expectedHeader)) {
              headerFound = true
              console.log(`找到列标题: ${expectedHeader} 在 ${String.fromCharCode(65 + j)}${i + 1}`)
              break
            }
          }
          if (headerFound) break
        }
        if (headerFound) foundHeaders++
      }

      // 至少要找到一半以上的期望标题
      return foundHeaders >= Math.ceil(expectedHeaders.length * 0.5)

    } catch (error) {
      console.error('检查列标题失败:', error)
      return false
    }
  }

  /**
   * 检查透视表总计值
   */
  private async checkTotalValue(worksheet: any, expectedTotal: number): Promise<boolean> {
    try {
      // 检查工作表中是否包含期望的总计值
      const range = worksheet.getRange('A1:Z50')
      const values = range.getValues()

      if (!values || values.length === 0) {
        return false
      }

      // 查找期望的总计值
      for (let i = 0; i < Math.min(values.length, 20); i++) {
        for (let j = 0; j < Math.min(values[i].length, 20); j++) {
          const cellValue = values[i][j]
          if (typeof cellValue === 'number' && Math.abs(cellValue - expectedTotal) < 0.01) {
            console.log(`找到期望的总计值: ${expectedTotal} 在 ${String.fromCharCode(65 + j)}${i + 1}`)
            return true
          }
        }
      }

      return false

    } catch (error) {
      console.error('检查总计值失败:', error)
      return false
    }
  }
}

/**
 * 创建验证服务实例
 */
export function createValidationService(univerAPI: UniverAPI): ExcelValidationService {
  return new ExcelValidationService(univerAPI)
}

/**
 * 验证任务的便捷函数
 */
export async function validateTask(univerAPI: UniverAPI, validationRule: ValidationRule): Promise<ValidationResult> {
  const service = createValidationService(univerAPI)
  return await service.validateTask(validationRule)
}

/**
 * 创建模拟的条件格式规则用于测试
 */
function createMockConditionalRules(): any[] {
  try {
    console.log('创建模拟条件格式规则')

    // 模拟3个条件格式规则
    const mockRules = [
      {
        rule: {
          operator: 'greaterThanOrEqual',
          value: 90,
          style: {
            bg: {
              rgb: '#0da471'  // 绿色
            }
          }
        },
        range: 'C2:C7'
      },
      {
        rule: {
          operator: 'between',
          minValue: 60,
          maxValue: 89,
          style: {
            bg: {
              rgb: '#fac815'  // 黄色
            }
          }
        },
        range: 'C2:C7'
      },
      {
        rule: {
          operator: 'lessThan',
          value: 60,
          style: {
            bg: {
              rgb: '#f05252'  // 红色
            }
          }
        },
        range: 'C2:C7'
      }
    ]

    console.log('模拟规则创建完成:', mockRules)
    return mockRules
  } catch (error) {
    console.error('创建模拟规则失败:', error)
    return []
  }
}