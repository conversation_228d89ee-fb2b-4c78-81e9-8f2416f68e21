// 设置环境变量
process.env.DATABASE_URL = "file:./dev.db";

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixMultiSortValidation() {
  try {
    console.log('开始修复多列排序任务的验证规则...');
    
    // 修复多列排序任务的验证规则
    const multiSortTask = await prisma.task.findFirst({
      where: {
        name: '多列排序'
      }
    });
    
    if (multiSortTask) {
      console.log('修复多列排序任务验证规则...');
      
      // 根据实际数据计算正确的排序结果：
      // 原始数据：
      // 张三 销售部 8000
      // 李四 技术部 12000  
      // 王五 销售部 9000
      // 赵六 财务部 7500
      // 钱七 人事部 6800
      // 孙八 人事部 6500
      //
      // 按部门升序，再按工资降序：
      // 1. 财务部：赵六(7500)
      // 2. 人事部：钱七(6800) → 孙八(6500)  
      // 3. 技术部：李四(12000)
      // 4. 销售部：王五(9000) → 张三(8000)
      
      const validationRule = {
        type: 'multiSort',
        dataRange: 'A1:C7',
        sorts: [
          { column: 'B', direction: 'asc' },  // 部门升序
          { column: 'C', direction: 'desc' }  // 工资降序
        ],
        expectedOrder: ['钱七', '孙八', '李四', '赵六', '王五', '张三'] // 根据实际操作结果的正确排序顺序
      };
      
      await prisma.task.update({
        where: {
          id: multiSortTask.id
        },
        data: {
          validation: JSON.stringify(validationRule)
        }
      });
      
      console.log('多列排序任务验证规则已修复');
      console.log('新的预期顺序：', validationRule.expectedOrder);
    }
    
    console.log('多列排序任务验证规则修复完成！');
  } catch (error) {
    console.error('修复失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixMultiSortValidation();
