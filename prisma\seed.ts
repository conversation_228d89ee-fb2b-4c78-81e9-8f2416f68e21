import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // 清空现有数据
  await prisma.userProgress.deleteMany()
  await prisma.task.deleteMany()
  await prisma.level.deleteMany()

  // 创建主任务分组的关卡和子任务
  const mainTasks = [
    {
      name: '基础入门',
      description: '学习Excel的基本操作和数据输入',
      difficulty: 1,
      order: 1,
      points: 100,
      subLevels: [
        {
          name: '基础入门 - 数据输入',
          description: '学习Excel的基本数据输入操作',
          difficulty: 1,
          order: 1,
          points: 50,
          tasks: [
            {
              name: '数据输入',
              description: '学习在Excel中输入文本数据\n\n操作步骤：\n1. 点击A1单元格（第一行第一列的单元格）\n2. 输入文本"Hello Excel"\n3. 按回车键确认输入\n\n完成后A1单元格应显示：Hello Excel',
              type: 'input',
              order: 1,
              validation: JSON.stringify({
                type: 'cellValue',
                cell: 'A1',
                expectedValue: 'Hello Excel'
              })
            }
          ]
        },
        {
          name: '基础入门 - 数字计算',
          description: '学习Excel的基本数字计算',
          difficulty: 1,
          order: 2,
          points: 50,
          tasks: [
            {
              name: '数字计算',
              description: '学习在Excel中进行基本的数字计算\n\n操作步骤：\n1. 点击B1单元格，输入数字10，按回车确认\n2. 点击B2单元格，输入数字20，按回车确认\n3. 点击B3单元格，输入公式=B1+B2，按回车确认\n\n完成后：\n- B1单元格显示：10\n- B2单元格显示：20\n- B3单元格显示：30（计算结果）',
              type: 'formula',
              order: 1,
              validation: JSON.stringify({
                type: 'cellFormula',
                cell: 'B3',
                expectedFormula: '=B1+B2',
                expectedValue: 30
              })
            }
          ]
        }
      ]
    },
    {
      name: '公式基础',
      description: '掌握Excel基础函数的使用',
      difficulty: 2,
      order: 2,
      points: 200,
      subLevels: [
        {
          name: '公式基础 - SUM函数',
          description: '掌握SUM函数的使用',
          difficulty: 2,
          order: 1,
          points: 100,
          tasks: [
            {
              name: 'SUM函数',
              description: '学习使用SUM函数计算一组数字的总和\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击A6单元格（第6行第1列）\n2. 输入公式=SUM(A1:A5)\n3. 按回车键确认\n\n完成后A6单元格应显示：150（A1到A5的总和）\n\n提示：SUM函数可以快速计算指定范围内所有数字的总和',
              type: 'formula',
              order: 1,
              validation: JSON.stringify({
                type: 'cellFormula',
                cell: 'A6',
                expectedFormula: '=SUM(A1:A5)'
              }),
              initialData: JSON.stringify({
                'A1': 10,
                'A2': 20,
                'A3': 30,
                'A4': 40,
                'A5': 50
              })
            }
          ]
        },
        {
          name: '公式基础 - AVERAGE函数',
          description: '掌握AVERAGE函数的使用',
          difficulty: 2,
          order: 2,
          points: 100,
          tasks: [
            {
              name: 'AVERAGE函数',
              description: '学习使用AVERAGE函数计算一组数字的平均值\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击B6单元格（第6行第2列）\n2. 输入公式=AVERAGE(B1:B5)\n3. 按回车键确认\n\n完成后B6单元格应显示：85（B1到B5的平均值）\n\n提示：AVERAGE函数可以自动计算指定范围内所有数字的平均值',
              type: 'formula',
              order: 1,
              validation: JSON.stringify({
                type: 'cellFormula',
                cell: 'B6',
                expectedFormula: '=AVERAGE(B1:B5)'
              }),
              initialData: JSON.stringify({
                'B1': 80,
                'B2': 90,
                'B3': 85,
                'B4': 95,
                'B5': 75
              })
            }
          ]
        }
      ]
    },
    {
      name: '数据格式化',
      description: '学习Excel数据格式设置和样式调整',
      difficulty: 2,
      order: 3,
      points: 150,
      subLevels: [
        {
          name: '数据格式化 - 数字格式',
          description: '学习数字格式设置',
          difficulty: 2,
          order: 1,
          points: 75,
          tasks: [
            {
              name: '数字格式',
              description: '学习设置单元格的数字格式\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 右键点击A1单元格\n2. 选择"设置单元格格式"\n3. 在"数字"选项卡中选择"货币"\n4. 点击"确定"按钮\n\n完成后A1单元格应显示为货币格式（如：￥1,234.56）\n\n提示：货币格式会自动添加货币符号和千位分隔符',
              type: 'format',
              order: 1,
              validation: JSON.stringify({
                type: 'cellFormat',
                cell: 'A1',
                expectedFormat: 'currency'
              }),
              initialData: JSON.stringify({
                'A1': 1234.56
              })
            }
          ]
        },
        {
          name: '数据格式化 - 字体样式',
          description: '学习字体样式设置',
          difficulty: 2,
          order: 2,
          points: 75,
          tasks: [
            {
              name: '粗体样式',
              description: '学习设置文字的粗体格式\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击选中B1单元格\n2. 在工具栏中点击粗体按钮（B）\n或者：\n1. 右键点击B1单元格\n2. 选择"设置单元格格式"\n3. 在"字体"选项卡中勾选"粗体"\n4. 点击"确定"\n\n完成后B1单元格中的文字应显示为粗体',
              type: 'format',
              order: 1,
              validation: JSON.stringify({
                type: 'cellStyle',
                cell: 'B1',
                expectedStyle: { bold: true }
              }),
              initialData: JSON.stringify({
                'B1': '重要文本'
              })
            },
            {
              name: '斜体样式',
              description: '学习设置文字的斜体格式\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击选中C1单元格\n2. 在工具栏中点击斜体按钮（I）\n或者：\n1. 右键点击C1单元格\n2. 选择"设置单元格格式"\n3. 在"字体"选项卡中勾选"斜体"\n4. 点击"确定"\n\n完成后C1单元格中的文字应显示为斜体',
              type: 'format',
              order: 2,
              validation: JSON.stringify({
                type: 'cellStyle',
                cell: 'C1',
                expectedStyle: { italic: true }
              }),
              initialData: JSON.stringify({
                'C1': '强调文本'
              })
            }
          ]
        }
      ]
    },
    {
      name: '条件函数',
      description: '掌握Excel条件判断和查找函数',
      difficulty: 3,
      order: 4,
      points: 300,
      subLevels: [
        {
          name: '条件函数 - IF函数',
          description: '掌握IF条件函数的使用',
          difficulty: 3,
          order: 1,
          points: 150,
          tasks: [
            {
              name: 'IF函数',
              description: '学习使用IF函数进行条件判断\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击B1单元格\n2. 输入公式=IF(A1>10,"大","小")\n3. 按回车键确认\n\n公式解释：\n- IF(条件, 真值, 假值)\n- 如果A1>10为真，显示"大"\n- 如果A1>10为假，显示"小"\n\n完成后B1单元格应显示：大（因为15>10）',
              type: 'formula',
              order: 1,
              validation: JSON.stringify({
                type: 'cellFormula',
                cell: 'B1',
                expectedFormula: '=IF(A1>10,"大","小")'
              }),
              initialData: JSON.stringify({
                'A1': 15
              })
            }
          ]
        },
        {
          name: '条件函数 - VLOOKUP函数',
          description: '掌握VLOOKUP查找函数的使用',
          difficulty: 3,
          order: 2,
          points: 150,
          tasks: [
            {
              name: 'VLOOKUP函数',
              description: '学习使用VLOOKUP函数进行数据查找\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击C1单元格\n2. 输入公式=VLOOKUP(A1,A:B,2,FALSE)\n3. 按回车键确认\n\n公式解释：\n- VLOOKUP(查找值, 查找范围, 列号, 精确匹配)\n- A1是要查找的值（苹果）\n- A:B是查找的数据范围\n- 2表示返回第2列的值（价格）\n- FALSE表示精确匹配\n\n完成后C1单元格应显示：5.5',
              type: 'formula',
              order: 1,
              validation: JSON.stringify({
                type: 'cellFormula',
                cell: 'C1',
                expectedFormula: '=VLOOKUP(A1,A:B,2,FALSE)'
              }),
              initialData: JSON.stringify({
                'A1': '苹果',
                'A2': '香蕉',
                'A3': '橙子',
                'B1': 5.5,
                'B2': 3.2,
                'B3': 4.8
              })
            }
          ]
        }
      ]
    },
    {
      name: '数据透视表',
      description: '创建和使用数据透视表分析数据',
      difficulty: 4,
      order: 5,
      points: 400,
      subLevels: [
        {
          name: '数据透视表',
          description: '创建和使用数据透视表分析数据',
          difficulty: 4,
          order: 1,
          points: 400,
          tasks: [
            {
              name: '创建透视表',
              description: '学习创建数据透视表来分析数据\n\n任务说明：\n表格中已有销售数据，请创建数据透视表进行分析。本任务要求创建包含特定内容的透视表。\n\n操作步骤：\n1. 选择数据范围A1:D6（包含表头和所有数据）\n2. 按鼠标右键，选择"数据透视表"\n3. 在弹出的对话框中：\n   - 确认数据范围为A1:D6\n   - 选择放置位置为新工作表\n   - 点击"确定"\n4. 在透视表字段面板中配置字段：\n   - 将"产品"字段拖拽到行区域\n   - 将"地区"字段拖拽到列区域\n   - 将"销售额"字段拖拽到值区域\n5. 确保透视表显示总计行和总计列\n\n验证要求：\n- 行标题必须包含：笔记本、台式机、总计\n- 列标题必须包含：北京、上海、广州、总计\n- 总计值应为：75000\n- 透视表必须包含产品、地区、销售额三个字段\n\n提示：\n- 本任务采用严格验证模式，需要完整配置透视表字段\n- 确保透视表包含所有必需的行标题、列标题和正确的总计值\n- 如果验证失败，请检查字段配置是否正确',
              type: 'pivot',
              order: 1,
              validation: JSON.stringify({
                type: 'pivotTable',
                expectedFields: ['产品', '地区', '销售额'],
                expectedRowHeaders: ['笔记本', '台式机', '总计'],
                expectedColumnHeaders: ['北京', '上海', '广州', '总计'],
                expectedTotalValue: 75000,
                strictValidation: true
              }),
              initialData: JSON.stringify({
                'A1': '产品',
                'B1': '地区',
                'C1': '季度',
                'D1': '销售额',
                'A2': '笔记本',
                'B2': '北京',
                'C2': 'Q1',
                'D2': 15000,
                'A3': '台式机',
                'B3': '上海',
                'C3': 'Q1',
                'D3': 12000,
                'A4': '笔记本',
                'B4': '广州',
                'C4': 'Q2',
                'D4': 18000,
                'A5': '台式机',
                'B5': '北京',
                'C5': 'Q2',
                'D5': 14000,
                'A6': '笔记本',
                'B6': '上海',
                'C6': 'Q3',
                'D6': 16000
              })
            }
          ]
        }
      ]
    },
    {
      name: '图表制作',
      description: '创建各种类型的图表来可视化数据',
      difficulty: 4,
      order: 6,
      points: 350,
      subLevels: [
        {
          name: '图表制作',
          description: '创建各种类型的图表来可视化数据',
          difficulty: 4,
          order: 1,
          points: 350,
          tasks: [
            {
              name: '柱状图',
              description: '学习创建柱状图来可视化数据\n\n任务说明：\n表格中已有月度销量数据，请创建柱状图进行可视化展示。\n\n操作步骤：\n1. 选择数据范围A1:B6（包含表头"月份"、"销量"和所有数据）\n2. 点击"插入"菜单\n3. 找到"图表"区域，点击"柱状图"按钮\n4. 选择任意一种柱状图样式（推荐选择第一个）\n5. 图表会自动插入到工作表中\n6. 可以拖拽调整图表位置和大小\n\n提示：\n- 确保选择的数据范围包含表头\n- 图表创建成功后系统会自动检测\n- 图表类型不限，柱状图、条形图都可以',
              type: 'chart',
              order: 1,
              validation: JSON.stringify({
                type: 'chart',
                expectedType: 'column',
                dataRange: 'A1:B6'
              }),
              initialData: JSON.stringify({
                'A1': '月份',
                'B1': '销量',
                'A2': '一月',
                'A3': '二月',
                'A4': '三月',
                'A5': '四月',
                'A6': '五月',
                'B2': 120,
                'B3': 150,
                'B4': 180,
                'B5': 200,
                'B6': 160
              })
            },
            {
              name: '饼图',
              description: '学习创建饼图来显示数据占比\n\n任务说明：\n表格中已有产品销售数据，请创建饼图显示各产品的销售占比。\n\n操作步骤：\n1. 选择数据范围A1:B5（包含产品名称和销售数据）\n2. 点击"插入"菜单\n3. 找到"图表"区域，点击"饼图"按钮\n4. 选择任意一种饼图样式\n5. 图表会自动插入到工作表中\n6. 可以调整图表位置和添加数据标签\n\n提示：\n- 饼图适合显示各部分占整体的比例\n- 数据会自动转换为百分比显示\n- 图表创建成功后系统会自动检测',
              type: 'chart',
              order: 2,
              validation: JSON.stringify({
                type: 'chart',
                expectedType: 'pie',
                dataRange: 'A1:B5'
              }),
              initialData: JSON.stringify({
                'A1': '产品A',
                'A2': '产品B',
                'A3': '产品C',
                'A4': '产品D',
                'A5': '产品E',
                'B1': 30,
                'B2': 25,
                'B3': 20,
                'B4': 15,
                'B5': 10
              })
            }
          ]
        }
      ]
    },
    {
      name: '高级函数',
      description: '掌握INDEX、MATCH、SUMIFS等高级函数',
      difficulty: 5,
      order: 7,
      points: 500,
      subLevels: [
        {
          name: '高级函数',
          description: '掌握INDEX、MATCH、SUMIFS等高级函数',
          difficulty: 5,
          order: 1,
          points: 500,
          tasks: [
            {
              name: 'INDEX+MATCH组合',
              description: '学习使用INDEX和MATCH函数组合进行高级查找\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击D1单元格\n2. 输入公式=INDEX(C:C,MATCH(A1,A:A,0))\n3. 按回车键确认\n\n公式解释：\n- MATCH(A1,A:A,0)：在A列中查找A1的位置\n- INDEX(C:C,位置)：返回C列中对应位置的值\n- 这种组合比VLOOKUP更灵活，可以向左查找\n\n完成后D1单元格应显示：8500',
              type: 'formula',
              order: 1,
              validation: JSON.stringify({
                type: 'cellFormula',
                cell: 'C1',
                expectedFormula: '=INDEX(C:C,MATCH(A1,A:A,0))'
              }),
              initialData: JSON.stringify({
                'A1': '张三',
                'A2': '李四',
                'A3': '王五',
                'A4': '赵六',
                'B1': '销售部',
                'B2': '技术部',
                'B3': '财务部',
                'B4': '人事部',
                'C1': 8500,
                'C2': 12000,
                'C3': 9500,
                'C4': 7800
              })
            },
            {
              name: 'SUMIFS函数',
              description: '学习使用SUMIFS函数进行多条件求和\n\n任务说明：\n表格中已有数据，请使用这些数据进行操作。\n\n操作步骤：\n1. 点击D1单元格\n2. 输入公式=SUMIFS(C:C,A:A,"销售部",B:B,"高级")\n3. 按回车键确认\n\n公式解释：\n- SUMIFS(求和范围, 条件范围1, 条件1, 条件范围2, 条件2)\n- C:C是要求和的工资列\n- A:A="销售部"是第一个条件\n- B:B="高级"是第二个条件\n\n完成后D1单元格应显示：17000（8000+9000）',
              type: 'formula',
              order: 2,
              validation: JSON.stringify({
                type: 'cellFormula',
                cell: 'D1',
                expectedFormula: '=SUMIFS(C:C,A:A,"销售部",B:B,"高级")'
              }),
              initialData: JSON.stringify({
                'A1': '销售部',
                'A2': '销售部',
                'A3': '技术部',
                'A4': '销售部',
                'A5': '技术部',
                'B1': '高级',
                'B2': '初级',
                'B3': '高级',
                'B4': '高级',
                'B5': '中级',
                'C1': 8000,
                'C2': 5000,
                'C3': 12000,
                'C4': 9000,
                'C5': 7000
              })
            }
          ]
        }
      ]
    },
    {
      name: '数据筛选',
      description: '掌握数据筛选技巧，快速查找所需信息',
      difficulty: 2,
      order: 8,
      points: 250,
      subLevels: [
        {
          name: '简单筛选',
          description: '学习单列数据筛选',
          difficulty: 2,
          order: 1,
          points: 125,
          tasks: [
            {
              name: '单列筛选',
              description: '学习对单列数据进行筛选\n\n任务说明：\n表格中已有员工数据，请对部门列进行筛选，只显示"销售部"的员工。\n\n操作步骤：\n1. 选择数据范围A1:C6（包含表头和所有数据）\n2. 点击"数据"菜单\n3. 点击"筛选"按钮（或使用快捷键Ctrl+Shift+L）\n4. 点击B1单元格（部门列）右侧出现的下拉箭头\n5. 在筛选菜单中：\n   - 取消选中"全选"\n   - 只勾选"销售部"\n   - 点击"确定"\n\n验证要求：\n- 筛选后应只显示3行数据（表头+2个销售部员工）\n- 显示的员工应为：张三、王五\n- 其他部门的员工应被隐藏\n\n提示：\n- 筛选不会删除数据，只是暂时隐藏不符合条件的行\n- 可以随时清除筛选恢复所有数据的显示',
              type: 'filter',
              order: 1,
              validation: JSON.stringify({
                type: 'filter',
                dataRange: 'A1:C6',
                expectedVisibleRows: 2, // 期望显示2行数据（不包括表头）
                expectedFilteredData: [
                  { 0: '张三', 1: '销售部', 2: 8000 },
                  { 0: '王五', 1: '销售部', 2: 9000 }
                ]
              }),
              initialData: JSON.stringify({
                'A1': '姓名',
                'B1': '部门',
                'C1': '工资',
                'A2': '张三',
                'B2': '销售部',
                'C2': 8000,
                'A3': '李四',
                'B3': '技术部',
                'C3': 12000,
                'A4': '王五',
                'B4': '销售部',
                'C4': 9000,
                'A5': '赵六',
                'B5': '财务部',
                'C5': 7500,
                'A6': '钱七',
                'B6': '人事部',
                'C6': 6800
              })
            }
          ]
        },
        {
          name: '复杂筛选',
          description: '学习多列数据筛选',
          difficulty: 3,
          order: 2,
          points: 125,
          tasks: [
            {
              name: '多列筛选',
              description: '学习对多列数据进行组合筛选\n\n任务说明：\n表格中已有产品销售数据，请同时筛选地区和季度，只显示"北京"地区"Q1"季度的数据。\n\n操作步骤：\n1. 选择数据范围A1:D7（包含表头和所有数据）\n2. 点击"数据"菜单，启用筛选功能\n3. 设置地区筛选：\n   - 点击B1单元格（地区列）的下拉箭头\n   - 取消"全选"，只勾选"北京"\n   - 点击"确定"\n4. 设置季度筛选：\n   - 点击C1单元格（季度列）的下拉箭头\n   - 取消"全选"，只勾选"Q1"\n   - 点击"确定"\n\n验证要求：\n- 筛选后应只显示2行数据（表头+1个符合条件的记录）\n- 显示的产品应为：笔记本\n- 销售额应为：15000\n\n提示：\n- 多列筛选是AND关系，必须同时满足所有条件\n- 筛选顺序不影响结果',
              type: 'filter',
              order: 1,
              validation: JSON.stringify({
                type: 'filter',
                dataRange: 'A1:D7',
                expectedVisibleRows: 2, // 期望显示2行数据（表头+1个符合条件的记录）
                expectedFilteredData: [
                  { 0: '笔记本', 1: '北京', 2: 'Q1', 3: 15000 },
                  { 0: '手机', 1: '北京', 2: 'Q1', 3: 22000 }
                ]
              }),
              initialData: JSON.stringify({
                'A1': '产品',
                'B1': '地区',
                'C1': '季度',
                'D1': '销售额',
                'A2': '笔记本',
                'B2': '北京',
                'C2': 'Q1',
                'D2': 15000,
                'A3': '台式机',
                'B3': '上海',
                'C3': 'Q1',
                'D3': 12000,
                'A4': '笔记本',
                'B4': '广州',
                'C4': 'Q2',
                'D4': 18000,
                'A5': '台式机',
                'B5': '北京',
                'C5': 'Q2',
                'D5': 14000,
                'A6': '平板',
                'B6': '上海',
                'C6': 'Q1',
                'D6': 8000,
                'A7': '手机',
                'B7': '北京',
                'C7': 'Q1',
                'D7': 22000
              })
            }
          ]
        }
      ]
    },
    {
      name: '数据排序',
      description: '掌握数据排序技巧，按需要的顺序整理数据',
      difficulty: 2,
      order: 9,
      points: 250,
      subLevels: [
        {
          name: '简单排序',
          description: '学习单列数据排序',
          difficulty: 2,
          order: 1,
          points: 125,
          tasks: [
            {
              name: '单列排序',
              description: '学习对单列数据进行排序\n\n任务说明：\n表格中已有学生成绩数据，请按成绩从高到低进行排序。\n\n操作步骤：\n1. 选择数据范围A1:C6（包含表头和所有数据）\n2. 点击"数据"菜单\n3. 点击"排序"按钮\n4. 在排序对话框中：\n   - 主要关键字选择"成绩"（C列）\n   - 排序依据选择"数值"\n   - 次序选择"降序"（从大到小）\n   - 点击"确定"\n\n验证要求：\n- 排序后第一行应为：李四（95分）\n- 排序后最后一行应为：赵六（78分）\n- 数据行的完整性应保持（姓名、班级、成绩对应关系不变）\n\n提示：\n- 排序会重新排列数据行的顺序\n- 确保选择完整的数据范围，避免数据错位',
              type: 'sort',
              order: 1,
              validation: JSON.stringify({
                type: 'sort',
                column: 'C',
                direction: 'desc',
                expectedOrder: ['李四', '王五', '钱七', '张三', '赵六']
              }),
              initialData: JSON.stringify({
                'A1': '姓名',
                'B1': '班级',
                'C1': '成绩',
                'A2': '张三',
                'B2': '一班',
                'C2': 85,
                'A3': '李四',
                'B3': '二班',
                'C3': 95,
                'A4': '王五',
                'B4': '一班',
                'C4': 92,
                'A5': '赵六',
                'B5': '三班',
                'C5': 78,
                'A6': '钱七',
                'B6': '二班',
                'C6': 88
              })
            }
          ]
        },
        {
          name: '复杂排序',
          description: '学习多列数据排序',
          difficulty: 3,
          order: 2,
          points: 125,
          tasks: [
            {
              name: '多列排序',
              description: '学习对多列数据进行组合排序\n\n任务说明：\n表格中已有员工数据，请先按部门升序排序，再按工资降序排序。\n\n操作步骤：\n1. 选择数据范围A1:C7（包含表头和所有数据）\n2. 点击"数据"菜单，点击"排序"\n3. 设置第一关键字：\n   - 主要关键字选择"部门"（B列）\n   - 排序依据选择"文本"\n   - 次序选择"升序"（A到Z）\n4. 添加第二关键字：\n   - 点击"添加条件"或"+"按钮\n   - 次要关键字选择"工资"（C列）\n   - 排序依据选择"数值"\n   - 次序选择"降序"（从大到小）\n5. 点击"确定"\n\n验证要求：\n- 财务部员工应排在最前面\n- 同部门内按工资从高到低排序\n- 预期顺序：赵六(财务)→李四(技术)→王五(销售)→张三(销售)→钱七(人事)→孙八(人事)\n\n提示：\n- 多级排序按优先级执行\n- 第一关键字相同时，按第二关键字排序',
              type: 'sort',
              order: 1,
              validation: JSON.stringify({
                type: 'multiSort',
                sorts: [
                  { column: 'B', direction: 'asc' },
                  { column: 'C', direction: 'desc' }
                ],
                expectedOrder: ['钱七', '孙八', '李四', '赵六', '王五', '张三']
              }),
              initialData: JSON.stringify({
                'A1': '姓名',
                'B1': '部门',
                'C1': '工资',
                'A2': '张三',
                'B2': '销售部',
                'C2': 8000,
                'A3': '李四',
                'B3': '技术部',
                'C3': 12000,
                'A4': '王五',
                'B4': '销售部',
                'C4': 9000,
                'A5': '赵六',
                'B5': '财务部',
                'C5': 7500,
                'A6': '钱七',
                'B6': '人事部',
                'C6': 6800,
                'A7': '孙八',
                'B7': '人事部',
                'C7': 6500
              })
            }
          ]
        }
      ]
    },
    {
      name: '条件格式',
      description: '使用条件格式突出显示重要数据',
      difficulty: 3,
      order: 10,
      points: 300,
      subLevels: [
        {
          name: '简单条件格式',
          description: '学习基础条件格式设置',
          difficulty: 3,
          order: 1,
          points: 150,
          tasks: [
            {
              name: '数值条件格式',
              description: '学习为数值设置条件格式\n\n任务说明：\n表格中已有销售数据，请为销售额大于10000的单元格设置红色背景。\n\n操作步骤：\n1. 选择数据范围C2:C6（销售额数据，不包含表头）\n2. 点击"数据"菜单\n3. 在"数据工具"组中点击"条件格式"\n4. 选择"突出显示单元格规则" → "样式规则设置为数字" → "大于"\n5. 在对话框中：\n   - 输入条件值：10000\n   - 在"填充"颜色选择框中选择第3行第3列的红色\n   - 点击"确定"\n\n验证要求：\n- C3单元格（12000）应显示红色背景\n- C5单元格（15000）应显示红色背景\n- 其他销售额单元格保持原样\n\n💡 提示：\n- 条件格式会根据数据变化自动更新\n- 可以设置多个条件格式规则',
              type: 'conditionalFormat',
              order: 1,
              validation: JSON.stringify({
                type: 'conditionalFormat',
                range: 'C2:C6',
                condition: 'greaterThan',
                value: 10000,
                expectedFormattedCells: ['C3', 'C5'],
                expectedBackgroundColor: '#f05252'
              }),
              initialData: JSON.stringify({
                'A1': '产品',
                'B1': '月份',
                'C1': '销售额',
                'A2': '笔记本',
                'B2': '1月',
                'C2': 8500,
                'A3': '台式机',
                'B3': '1月',
                'C3': 12000,
                'A4': '平板',
                'B4': '1月',
                'C4': 6800,
                'A5': '手机',
                'B5': '1月',
                'C5': 15000,
                'A6': '耳机',
                'B6': '1月',
                'C6': 3200
              })
            }
          ]
        },
        {
          name: '复杂条件格式',
          description: '学习高级条件格式设置',
          difficulty: 4,
          order: 2,
          points: 150,
          tasks: [
            {
              name: '多条件格式',
              description: '学习设置多个条件格式规则\n\n任务说明：\n表格中已有学生成绩数据，请设置以下条件格式：\n- 成绩≥90：绿色背景\n- 成绩60-89：黄色背景\n- 成绩<60：红色背景\n\n操作步骤：\n1. 选择数据范围C2:C7（成绩数据）\n2. 设置第一个条件（优秀）：\n   - 点击"数据"菜单中的"条件格式"\n   - 选择"突出显示单元格规则" → "样式规则设置为数字" → "大于或等于"\n   - 输入90，在"填充"颜色选择框中选择第6列第3行的绿色\n   - 点击"确定"\n3. 设置第二个条件（及格）：\n   - 再次点击"条件格式" → "样式规则设置为数字" → "突出显示单元格规则" → "样式规则设置为数字"  → "介于"\n   - 输入60到89，在"填充"颜色选择框中选择第5列第3行的黄色\n   - 点击"确定"\n4. 设置第三个条件（不及格）：\n   - 点击"条件格式" → "突出显示单元格规则" → "样式规则设置为数字" → "小于"\n   - 输入60，在"填充"颜色选择框中选择第3列第3行的红色\n   - 点击"确定"\n\n验证要求：\n- 95分、92分应显示绿色背景\n- 85分、78分、88分应显示黄色背景\n- 45分应显示红色背景\n\n💡 提示：\n- 条件格式按优先级执行，后设置的规则优先级更高\n- 可以通过"管理规则"调整优先级',
              type: 'conditionalFormat',
              order: 1,
              validation: JSON.stringify({
                type: 'multiConditionalFormat',
                range: 'C2:C7',
                conditions: [
                  { type: 'greaterThanOrEqual', value: 90, color: '#0da471' },
                  { type: 'between', minValue: 60, maxValue: 89, color: '#fac815' },
                  { type: 'lessThan', value: 60, color: '#f05252' }
                ],
                expectedResults: {
                  '#0da471': ['C3', 'C4'],
                  '#fac815': ['C2', 'C5', 'C6'],
                  '#f05252': ['C7']
                }
              }),
              initialData: JSON.stringify({
                'A1': '姓名',
                'B1': '班级',
                'C1': '成绩',
                'A2': '张三',
                'B2': '一班',
                'C2': 85,
                'A3': '李四',
                'B3': '二班',
                'C3': 95,
                'A4': '王五',
                'B4': '一班',
                'C4': 92,
                'A5': '赵六',
                'B5': '三班',
                'C5': 78,
                'A6': '钱七',
                'B6': '二班',
                'C6': 88,
                'A7': '孙八',
                'B7': '三班',
                'C7': 45
              })
            }
          ]
        }
      ]
    }
  ]

  // 创建主任务和子任务
  for (const mainTask of mainTasks) {
    const { subLevels, ...mainTaskInfo } = mainTask
    
    // 创建主任务（作为父级关卡）
    const mainLevel = await prisma.level.create({
      data: {
        ...mainTaskInfo,
        isMainTask: true // 标记为主任务
      }
    })

    // 创建子任务（子关卡）
    for (const subLevel of subLevels) {
      const { tasks, ...subLevelInfo } = subLevel
      
      const level = await prisma.level.create({
        data: {
          ...subLevelInfo,
          parentId: mainLevel.id, // 关联到主任务
          isMainTask: false // 标记为子任务
        }
      })

      // 创建具体的练习任务
      for (const taskData of tasks) {
        await prisma.task.create({
          data: {
            ...taskData,
            levelId: level.id
          }
        })
      }
    }
  }

  console.log('数据库种子数据创建完成！')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })