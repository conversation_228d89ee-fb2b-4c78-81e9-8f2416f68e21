console.log('测试严格的筛选验证逻辑...\n');

// 模拟Univer API
class MockUniverAPI {
  constructor(testData, hasFilter = false, visibleRows = null) {
    this.testData = testData;
    this.hasFilterFlag = hasFilter;
    this.visibleRowsData = visibleRows;
  }
  
  getActiveWorkbook() {
    return {
      getActiveSheet: () => ({
        getRange: (range) => ({
          getValues: () => this.testData,
          getValue: () => this.testData[0] && this.testData[0][0],
          getCellData: () => ({ v: this.testData[0] && this.testData[0][0] })
        }),
        hasFilter: () => this.hasFilterFlag,
        getVisibleRows: () => this.visibleRowsData || this.testData
      })
    };
  }
}

// 模拟DOM环境
function mockDOM(hasFilterButtons = false, hasHiddenRows = false, hasActiveFilters = false) {
  global.document = {
    querySelectorAll: (selector) => {
      if (selector.includes('filter-button') || selector.includes('filter-dropdown')) {
        return hasFilterButtons ? [{}] : [];
      }
      if (selector.includes('display: none') || selector.includes('hidden-row')) {
        return hasHiddenRows ? [{}] : [];
      }
      if (selector.includes('filter-active') || selector.includes('filtered')) {
        return hasActiveFilters ? [{}] : [];
      }
      return [];
    }
  };
}

// 简化的验证逻辑测试
function testFilterValidationLogic(testCase) {
  console.log(`=== ${testCase.name} ===`);
  
  // 设置模拟环境
  mockDOM(testCase.hasFilterButtons, testCase.hasHiddenRows, testCase.hasActiveFilters);
  
  const mockAPI = new MockUniverAPI(testCase.data, testCase.hasFilter, testCase.visibleRows);
  
  // 模拟验证逻辑
  const values = testCase.data;
  const hasHeader = values.length > 0 && values[0] && 
    values[0].some(cell => typeof cell === 'string' && isNaN(Number(cell)));
  
  const dataStartRow = hasHeader ? 1 : 0;
  const originalDataRows = values.slice(dataStartRow);
  
  let isFilterApplied = false;
  let actualVisibleData = [];
  let filterDetectionMethod = '';
  
  // 方法1: API检测
  try {
    const hasAutoFilter = testCase.hasFilter;
    if (hasAutoFilter) {
      const visibleRows = testCase.visibleRows || testCase.data;
      if (visibleRows && visibleRows.length > 0) {
        const visibleDataRows = visibleRows.slice(dataStartRow);
        if (visibleDataRows.length < originalDataRows.length) {
          isFilterApplied = true;
          actualVisibleData = visibleDataRows;
          filterDetectionMethod = 'Univer API';
        }
      }
    }
  } catch (error) {
    // API检测失败
  }
  
  // 方法2: DOM检测
  if (!isFilterApplied) {
    const hasFilterElements = testCase.hasFilterButtons && testCase.hasHiddenRows;
    const hasActiveFilterElements = testCase.hasActiveFilters;
    
    if (hasFilterElements || hasActiveFilterElements) {
      isFilterApplied = true;
      filterDetectionMethod = 'DOM检测';
    }
  }
  
  console.log('原始数据行数:', originalDataRows.length);
  console.log('筛选检测结果:', isFilterApplied);
  console.log('检测方法:', filterDetectionMethod);
  console.log('可见数据行数:', actualVisibleData.length);
  
  // 验证结果
  if (!isFilterApplied) {
    console.log('结果: ✗ 未检测到筛选操作');
    return false;
  }
  
  // 验证行数
  if (testCase.expectedVisibleRows !== undefined) {
    if (actualVisibleData.length !== testCase.expectedVisibleRows) {
      console.log(`结果: ✗ 行数不匹配 (期望: ${testCase.expectedVisibleRows}, 实际: ${actualVisibleData.length})`);
      return false;
    }
  }
  
  // 验证数据内容
  if (testCase.expectedFilteredData && testCase.expectedFilteredData.length > 0) {
    if (actualVisibleData.length !== testCase.expectedFilteredData.length) {
      console.log(`结果: ✗ 数据行数不匹配 (期望: ${testCase.expectedFilteredData.length}, 实际: ${actualVisibleData.length})`);
      return false;
    }
    
    // 检查数据内容
    let allMatch = true;
    for (let i = 0; i < testCase.expectedFilteredData.length; i++) {
      const expectedRow = testCase.expectedFilteredData[i];
      let foundMatch = false;
      
      for (let j = 0; j < actualVisibleData.length; j++) {
        const actualRow = actualVisibleData[j];
        let rowMatches = true;
        
        for (const key in expectedRow) {
          const colIndex = parseInt(key);
          const expectedValue = expectedRow[key];
          const actualValue = actualRow[colIndex];
          
          if (actualValue !== expectedValue) {
            rowMatches = false;
            break;
          }
        }
        
        if (rowMatches) {
          foundMatch = true;
          break;
        }
      }
      
      if (!foundMatch) {
        allMatch = false;
        break;
      }
    }
    
    if (!allMatch) {
      console.log('结果: ✗ 数据内容不匹配');
      return false;
    }
  }
  
  console.log('结果: ✓ 验证通过');
  return true;
}

// 测试用例
const testCases = [
  {
    name: '测试1: 无筛选操作 - 应该失败',
    data: [
      ['姓名', '部门', '工资'],
      ['张三', '销售部', 8000],
      ['李四', '技术部', 12000],
      ['王五', '销售部', 9000],
      ['赵六', '财务部', 7500]
    ],
    hasFilter: false,
    hasFilterButtons: false,
    hasHiddenRows: false,
    hasActiveFilters: false,
    visibleRows: null,
    expectedVisibleRows: 2,
    expectedFilteredData: [
      { 0: '张三', 1: '销售部', 2: 8000 },
      { 0: '王五', 1: '销售部', 2: 9000 }
    ]
  },
  {
    name: '测试2: 有筛选器但无筛选条件 - 应该失败',
    data: [
      ['姓名', '部门', '工资'],
      ['张三', '销售部', 8000],
      ['李四', '技术部', 12000],
      ['王五', '销售部', 9000],
      ['赵六', '财务部', 7500]
    ],
    hasFilter: true,
    hasFilterButtons: true,
    hasHiddenRows: false,
    hasActiveFilters: false,
    visibleRows: [
      ['姓名', '部门', '工资'],
      ['张三', '销售部', 8000],
      ['李四', '技术部', 12000],
      ['王五', '销售部', 9000],
      ['赵六', '财务部', 7500]
    ],
    expectedVisibleRows: 2,
    expectedFilteredData: [
      { 0: '张三', 1: '销售部', 2: 8000 },
      { 0: '王五', 1: '销售部', 2: 9000 }
    ]
  },
  {
    name: '测试3: 正确的筛选操作 - 应该成功',
    data: [
      ['姓名', '部门', '工资'],
      ['张三', '销售部', 8000],
      ['李四', '技术部', 12000],
      ['王五', '销售部', 9000],
      ['赵六', '财务部', 7500]
    ],
    hasFilter: true,
    hasFilterButtons: true,
    hasHiddenRows: true,
    hasActiveFilters: true,
    visibleRows: [
      ['姓名', '部门', '工资'],
      ['张三', '销售部', 8000],
      ['王五', '销售部', 9000]
    ],
    expectedVisibleRows: 2,
    expectedFilteredData: [
      { 0: '张三', 1: '销售部', 2: 8000 },
      { 0: '王五', 1: '销售部', 2: 9000 }
    ]
  },
  {
    name: '测试4: 筛选结果错误 - 应该失败',
    data: [
      ['产品', '地区', '季度', '销售额'],
      ['笔记本', '北京', 'Q1', 15000],
      ['台式机', '上海', 'Q1', 12000],
      ['手机', '北京', 'Q1', 22000]
    ],
    hasFilter: true,
    hasFilterButtons: true,
    hasHiddenRows: true,
    hasActiveFilters: true,
    visibleRows: [
      ['产品', '地区', '季度', '销售额'],
      ['台式机', '上海', 'Q1', 12000]  // 错误的筛选结果
    ],
    expectedVisibleRows: 2,
    expectedFilteredData: [
      { 0: '笔记本', 1: '北京', 2: 'Q1', 3: 15000 },
      { 0: '手机', 1: '北京', 2: 'Q1', 3: 22000 }
    ]
  }
];

// 运行测试
console.log('开始测试严格的筛选验证逻辑...\n');

let passedTests = 0;
let totalTests = testCases.length;

for (const testCase of testCases) {
  const result = testFilterValidationLogic(testCase);
  
  // 检查测试结果是否符合预期
  const shouldPass = testCase.name.includes('应该成功');
  const shouldFail = testCase.name.includes('应该失败');
  
  if ((shouldPass && result) || (shouldFail && !result)) {
    passedTests++;
    console.log('测试预期: ✓ 符合预期\n');
  } else {
    console.log('测试预期: ✗ 不符合预期\n');
  }
}

console.log('=== 测试总结 ===');
console.log(`通过测试: ${passedTests}/${totalTests}`);
console.log(`测试结果: ${passedTests === totalTests ? '✓ 所有测试通过' : '✗ 存在问题'}`);

if (passedTests === totalTests) {
  console.log('\n严格的筛选验证逻辑修复成功！');
  console.log('- 无筛选操作时正确拒绝');
  console.log('- 有筛选器但无筛选条件时正确拒绝');
  console.log('- 正确筛选操作时正确通过');
  console.log('- 筛选结果错误时正确拒绝');
} else {
  console.log('\n验证逻辑仍需进一步调整。');
}
