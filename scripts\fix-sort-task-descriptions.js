// 设置环境变量
process.env.DATABASE_URL = "file:./dev.db";

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixSortTaskDescriptions() {
  try {
    console.log('开始修复排序任务的操作说明...');

    // 首先检查数据库连接
    await prisma.$connect();
    console.log('数据库连接成功');
    
    // 修复单列排序任务
    const singleSortTask = await prisma.task.findFirst({
      where: {
        name: '单列排序'
      }
    });
    
    if (singleSortTask) {
      console.log('找到单列排序任务，更新操作说明...');
      
      const updatedDescription = `学习对单列数据进行排序

任务说明：
表格中已有学生成绩数据，请按成绩从高到低进行排序。

操作步骤：
1. 选择数据范围A1:C6（包含表头和所有数据）
2. 点击工具栏上的"数据"标签
3. 点击"排序"图标（带有上下箭头的图标）
4. 在弹出的排序菜单中选择"自定义排序"
5. 在排序提醒对话框中选择"拓展排序范围"，点击"确认"
6. 在自定义排序对话框中：
   - 主要关键字选择"成绩"（C列）
   - 排序依据选择"数值"
   - 次序选择"降序"（从大到小）
   - 点击"确定"

验证要求：
- 排序后第一行应为：李四（95分）
- 排序后最后一行应为：赵六（78分）
- 数据行的完整性应保持（姓名、班级、成绩对应关系不变）

提示：
- 排序会重新排列数据行的顺序
- 确保选择完整的数据范围，避免数据错位
- 如果排序对话框位置不当，可以拖拽移动到合适位置`;

      await prisma.task.update({
        where: {
          id: singleSortTask.id
        },
        data: {
          description: updatedDescription
        }
      });
      
      console.log('单列排序任务操作说明已更新');
    } else {
      console.log('未找到单列排序任务');
    }
    
    // 修复多列排序任务
    const multiSortTask = await prisma.task.findFirst({
      where: {
        name: '多列排序'
      }
    });
    
    if (multiSortTask) {
      console.log('找到多列排序任务，更新操作说明...');
      
      const updatedDescription = `学习对多列数据进行组合排序

任务说明：
表格中已有员工数据，请先按部门升序排序，再按工资降序排序。

操作步骤：
1. 选择数据范围A1:C7（包含表头和所有数据）
2. 点击工具栏上的"数据"标签
3. 点击"排序"图标（带有上下箭头的图标）
4. 在弹出的排序菜单中选择"自定义排序"
5. 在排序提醒对话框中选择"拓展排序范围"，点击"确认"
6. 在自定义排序对话框中设置多级排序：
   - 第一关键字：选择"部门"（B列），排序依据选择"文本"，次序选择"升序"（A到Z）
   - 点击"添加条件"或"+"按钮添加第二关键字
   - 第二关键字：选择"工资"（C列），排序依据选择"数值"，次序选择"降序"（从大到小）
   - 点击"确定"

验证要求：
- 财务部员工应排在最前面
- 同部门内按工资从高到低排序
- 预期顺序：赵六(财务)→李四(技术)→王五(销售)→张三(销售)→钱七(人事)→孙八(人事)

提示：
- 多级排序按优先级执行
- 第一关键字相同时，按第二关键字排序
- 如果排序对话框位置不当，可以拖拽移动到合适位置`;

      await prisma.task.update({
        where: {
          id: multiSortTask.id
        },
        data: {
          description: updatedDescription
        }
      });
      
      console.log('多列排序任务操作说明已更新');
    } else {
      console.log('未找到多列排序任务');
    }
    
    console.log('排序任务操作说明修复完成！');
  } catch (error) {
    console.error('修复失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixSortTaskDescriptions();
