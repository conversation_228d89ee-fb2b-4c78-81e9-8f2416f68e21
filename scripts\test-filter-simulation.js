// 测试筛选功能的模拟脚本
// 这个脚本将在浏览器中运行，模拟用户的筛选操作

function simulateFilterOperation() {
  console.log('开始模拟筛选操作...');
  
  try {
    // 检查Univer实例是否可用
    if (!window.univerAPI) {
      console.error('Univer API 不可用');
      return false;
    }
    
    const workbook = window.univerAPI.getActiveWorkbook();
    const worksheet = workbook.getActiveSheet();
    
    console.log('获取到工作表:', worksheet);
    
    // 获取当前数据
    const range = worksheet.getRange('A1:C6');
    const values = range.getValues();
    console.log('当前数据:', values);
    
    // 模拟筛选操作：只保留销售部的员工
    const filteredData = [
      ['姓名', '部门', '工资'], // 表头
      ['张三', '销售部', 8000],
      ['王五', '销售部', 9000]
    ];
    
    // 尝试设置筛选后的数据
    const filteredRange = worksheet.getRange('A1:C3');
    filteredRange.setValues(filteredData);
    
    console.log('筛选操作模拟完成');
    return true;
    
  } catch (error) {
    console.error('模拟筛选操作失败:', error);
    return false;
  }
}

function testFilterValidation() {
  console.log('开始测试筛选验证逻辑...');
  
  // 模拟筛选操作
  const filterSuccess = simulateFilterOperation();
  
  if (filterSuccess) {
    console.log('筛选操作模拟成功，现在可以测试验证逻辑');
    
    // 等待一段时间让数据更新
    setTimeout(() => {
      // 触发提交按钮点击
      const submitButton = document.querySelector('button:contains("提交任务")') || 
                          document.querySelector('[data-testid="submit-task"]') ||
                          Array.from(document.querySelectorAll('button')).find(btn => 
                            btn.textContent.includes('提交任务') || btn.textContent.includes('提交')
                          );
      
      if (submitButton) {
        console.log('找到提交按钮，准备测试验证逻辑');
        submitButton.click();
      } else {
        console.log('未找到提交按钮');
      }
    }, 2000);
  } else {
    console.log('筛选操作模拟失败');
  }
}

// 导出函数供浏览器控制台使用
window.simulateFilterOperation = simulateFilterOperation;
window.testFilterValidation = testFilterValidation;

console.log('筛选测试脚本已加载。可以调用 testFilterValidation() 来测试。');
