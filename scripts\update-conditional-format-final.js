// 最终更新条件格式任务的颜色配置和操作说明
process.env.DATABASE_URL = "file:./prisma/dev.db"

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function updateConditionalFormatFinal() {
  try {
    console.log('开始最终更新条件格式任务...')
    
    // 更新简单条件格式任务
    const simpleTask = await prisma.task.findFirst({
      where: {
        name: '数值条件格式'
      }
    })
    
    if (simpleTask) {
      console.log('找到简单条件格式任务，更新配置...')
      
      // 更新操作说明，删除指定的两条，修改自定义格式描述
      const updatedSimpleDescription = `学习为数值设置条件格式

任务说明：
表格中已有销售数据，请为销售额大于10000的单元格设置红色背景。

操作步骤：
1. 选择数据范围C2:C6（销售额数据，不包含表头）
2. 点击"数据"菜单
3. 在"数据工具"组中点击"条件格式"
4. 选择"突出显示单元格规则" → "样式规则设置为数字" → "大于"
5. 在对话框中：
   - 输入条件值：10000
   - 在"填充"颜色选择框中选择第3行第3列的红色
   - 点击"确定"

验证要求：
- C3单元格（12000）应显示红色背景
- C5单元格（15000）应显示红色背景
- 其他销售额单元格保持原样

💡 提示：
- 条件格式会根据数据变化自动更新
- 可以设置多个条件格式规则`

      // 更新验证规则，使用新的背景颜色
      const updatedSimpleValidation = {
        type: 'conditionalFormat',
        range: 'C2:C6',
        condition: 'greaterThan',
        value: 10000,
        expectedFormattedCells: ['C3', 'C5'],
        expectedBackgroundColor: '#f05252'
      }
      
      await prisma.task.update({
        where: {
          id: simpleTask.id
        },
        data: {
          description: updatedSimpleDescription,
          validation: JSON.stringify(updatedSimpleValidation)
        }
      })
      
      console.log('✅ 简单条件格式任务已更新')
      console.log('   - 背景色：#f05252（第3行第3列的红色）')
      console.log('   - 删除了"浅红填充色深红色文本"说明')
      console.log('   - 删除了"建议选择明显的红色背景色"说明')
      console.log('   - 修改了自定义格式描述')
    } else {
      console.log('❌ 未找到简单条件格式任务')
    }
    
    // 更新复杂条件格式任务
    const complexTask = await prisma.task.findFirst({
      where: {
        name: '多条件格式'
      }
    })
    
    if (complexTask) {
      console.log('找到复杂条件格式任务，更新配置...')
      
      // 更新操作说明，明确颜色选择位置
      const updatedComplexDescription = `学习设置多个条件格式规则

任务说明：
表格中已有学生成绩数据，请设置以下条件格式：
- 成绩≥90：绿色背景（#0da471，颜色选择框中的第6列第3行）
- 成绩60-89：黄色背景（#fac815，颜色选择框中的第5列第3行）
- 成绩<60：红色背景（#f05252，颜色选择框中的第3列第3行）

操作步骤：
1. 选择数据范围C2:C7（成绩数据）
2. 设置第一个条件（优秀）：
   - 点击"数据"菜单中的"条件格式"
   - 选择"突出显示单元格规则" → "大于或等于"
   - 输入90，在"填充"颜色选择框中选择第6列第3行的绿色
   - 点击"确定"
3. 设置第二个条件（及格）：
   - 再次点击"条件格式" → "突出显示单元格规则" → "介于"
   - 输入60到89，在"填充"颜色选择框中选择第5列第3行的黄色
   - 点击"确定"
4. 设置第三个条件（不及格）：
   - 点击"条件格式" → "突出显示单元格规则" → "小于"
   - 输入60，在"填充"颜色选择框中选择第3列第3行的红色
   - 点击"确定"

验证要求：
- 95分、92分应显示绿色背景
- 85分、78分、88分应显示黄色背景
- 45分应显示红色背景

💡 提示：
- 条件格式按优先级执行，后设置的规则优先级更高
- 可以通过"管理规则"调整优先级`

      // 更新验证规则，使用新的背景颜色
      const updatedComplexValidation = {
        type: 'multiConditionalFormat',
        range: 'C2:C7',
        conditions: [
          { type: 'greaterThanOrEqual', value: 90, color: '#0da471' },
          { type: 'between', minValue: 60, maxValue: 89, color: '#fac815' },
          { type: 'lessThan', value: 60, color: '#f05252' }
        ],
        expectedResults: {
          '#0da471': ['C3', 'C4'],
          '#fac815': ['C2', 'C5', 'C6'],
          '#f05252': ['C7']
        }
      }
      
      await prisma.task.update({
        where: {
          id: complexTask.id
        },
        data: {
          description: updatedComplexDescription,
          validation: JSON.stringify(updatedComplexValidation)
        }
      })
      
      console.log('✅ 复杂条件格式任务已更新')
      console.log('   - 绿色：#0da471（第6列第3行）')
      console.log('   - 黄色：#fac815（第5列第3行）')
      console.log('   - 红色：#f05252（第3列第3行）')
      console.log('   - 明确了颜色选择框位置')
    } else {
      console.log('❌ 未找到复杂条件格式任务')
    }
    
    console.log('\n🎉 条件格式任务配置更新完成！')
    console.log('\n📋 更新总结：')
    console.log('✅ 简单条件格式：背景色改为 #f05252')
    console.log('✅ 复杂条件格式：绿色 #0da471、黄色 #fac815、红色 #f05252')
    console.log('✅ 删除了"浅红填充色深红色文本"和"建议选择明显的红色背景色"说明')
    console.log('✅ 修改了自定义格式描述为"填充颜色选择框"')
    console.log('✅ 明确了颜色选择框的具体位置（第几行第几列）')
    
  } catch (error) {
    console.error('❌ 更新失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updateConditionalFormatFinal()
