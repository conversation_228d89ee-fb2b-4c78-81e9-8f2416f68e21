// 设置环境变量
process.env.DATABASE_URL = "file:./dev.db"

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function updateConditionalFormatInstructions() {
  try {
    console.log('开始更新条件格式操作说明...')
    console.log('DATABASE_URL:', process.env.DATABASE_URL)
    console.log('Prisma client created successfully')
    
    // 更新简单条件格式任务
    const simpleTask = await prisma.task.findFirst({
      where: {
        name: '数值条件格式'
      }
    })
    
    if (simpleTask) {
      console.log('找到简单条件格式任务，更新操作说明...')
      
      // 更新操作说明，修正菜单位置和颜色选择描述
      const updatedSimpleDescription = `学习为数值设置条件格式

任务说明：
表格中已有销售数据，请为销售额大于10000的单元格设置红色背景。

操作步骤：
1. 选择数据范围C2:C6（销售额数据，不包含表头）
2. 点击"数据"菜单
3. 在"数据工具"组中点击"条件格式"
4. 选择"突出显示单元格规则" → "大于"
5. 在对话框中：
   - 输入条件值：10000
   - 在格式下拉框中选择"浅红填充色深红色文本"
   - 或点击"自定义格式"，在"填充"选项卡中选择第3行第3列的红色
   - 点击"确定"

验证要求：
- C3单元格（12000）应显示红色背景
- C5单元格（15000）应显示红色背景
- 其他销售额单元格保持原样

提示：
- 条件格式会根据数据变化自动更新
- 可以设置多个条件格式规则
- 建议选择明显的红色背景色以便验证`

      await prisma.task.update({
        where: {
          id: simpleTask.id
        },
        data: {
          description: updatedSimpleDescription
        }
      })
      
      console.log('简单条件格式任务操作说明已更新')
    } else {
      console.log('未找到简单条件格式任务')
    }
    
    // 更新复杂条件格式任务
    const complexTask = await prisma.task.findFirst({
      where: {
        name: '多条件格式'
      }
    })
    
    if (complexTask) {
      console.log('找到复杂条件格式任务，更新操作说明...')
      
      // 更新操作说明，修正菜单位置和颜色选择描述
      const updatedComplexDescription = `学习设置多个条件格式规则

任务说明：
表格中已有学生成绩数据，请设置以下条件格式：
- 成绩≥90：绿色背景
- 成绩60-89：黄色背景
- 成绩<60：红色背景

操作步骤：
1. 选择数据范围C2:C7（成绩数据）
2. 设置第一个条件（优秀）：
   - 点击"数据"菜单 → "条件格式" → "突出显示单元格规则" → "大于或等于"
   - 输入90
   - 在格式下拉框中选择"绿色填充色深绿色文本"
   - 或点击"自定义格式"，在"填充"选项卡中选择第3行第4列的绿色
   - 点击"确定"
3. 设置第二个条件（及格）：
   - 再次点击"数据"菜单 → "条件格式" → "突出显示单元格规则" → "介于"
   - 输入60到89
   - 在格式下拉框中选择"黄色填充色深黄色文本"
   - 或点击"自定义格式"，在"填充"选项卡中选择第3行第5列的黄色
   - 点击"确定"
4. 设置第三个条件（不及格）：
   - 点击"数据"菜单 → "条件格式" → "突出显示单元格规则" → "小于"
   - 输入60
   - 在格式下拉框中选择"浅红填充色深红色文本"
   - 或点击"自定义格式"，在"填充"选项卡中选择第3行第3列的红色
   - 点击"确定"

验证要求：
- 95分、92分应显示绿色背景
- 85分、78分、88分应显示黄色背景
- 45分应显示红色背景

提示：
- 条件格式按优先级执行，后设置的规则优先级更高
- 可以通过"管理规则"调整优先级
- 使用明显的颜色便于验证结果`

      await prisma.task.update({
        where: {
          id: complexTask.id
        },
        data: {
          description: updatedComplexDescription
        }
      })
      
      console.log('复杂条件格式任务操作说明已更新')
    } else {
      console.log('未找到复杂条件格式任务')
    }
    
    console.log('条件格式操作说明更新完成！')
  } catch (error) {
    console.error('更新失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updateConditionalFormatInstructions()
