// 设置环境变量
process.env.DATABASE_URL = "file:./dev.db";

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateSortValidation() {
  try {
    console.log('开始更新排序任务的验证规则...');
    
    // 更新单列排序任务的验证规则
    const singleSortTask = await prisma.task.findFirst({
      where: {
        name: '单列排序'
      }
    });
    
    if (singleSortTask) {
      console.log('更新单列排序任务验证规则...');
      
      const validationRule = {
        type: 'sort',
        dataRange: 'A1:C6',
        column: 'C',
        direction: 'desc',
        expectedOrder: ['李四', '王五', '钱七', '张三', '赵六'] // 按成绩降序排列的姓名顺序：95, 92, 88, 85, 78
      };
      
      await prisma.task.update({
        where: {
          id: singleSortTask.id
        },
        data: {
          validation: JSON.stringify(validationRule)
        }
      });
      
      console.log('单列排序任务验证规则已更新');
    }
    
    // 更新多列排序任务的验证规则
    const multiSortTask = await prisma.task.findFirst({
      where: {
        name: '多列排序'
      }
    });
    
    if (multiSortTask) {
      console.log('更新多列排序任务验证规则...');
      
      const validationRule = {
        type: 'multiSort',
        dataRange: 'A1:C7',
        sorts: [
          { column: 'B', direction: 'asc' },  // 部门升序
          { column: 'C', direction: 'desc' }  // 工资降序
        ],
        expectedOrder: ['赵六', '李四', '王五', '张三', '钱七', '孙八'] // 预期的姓名顺序
      };
      
      await prisma.task.update({
        where: {
          id: multiSortTask.id
        },
        data: {
          validation: JSON.stringify(validationRule)
        }
      });
      
      console.log('多列排序任务验证规则已更新');
    }
    
    console.log('排序任务验证规则更新完成！');
  } catch (error) {
    console.error('更新失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateSortValidation();
