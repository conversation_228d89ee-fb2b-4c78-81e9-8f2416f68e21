import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    console.log('开始更新条件格式任务配置...')
    
    // 更新简单条件格式任务
    const simpleTaskUpdate = await prisma.task.updateMany({
      where: {
        name: '数值条件格式'
      },
      data: {
        description: `学习为数值设置条件格式

任务说明：
表格中已有销售数据，请为销售额大于10000的单元格设置红色背景。

操作步骤：
1. 选择数据范围C2:C6（销售额数据，不包含表头）
2. 点击"数据"菜单
3. 在"数据工具"组中点击"条件格式"
4. 选择"突出显示单元格规则" → "样式规则设置为数字" → "大于"
5. 在对话框中：
   - 输入条件值：10000
   - 在"填充"颜色选择框中选择第3行第3列的红色
   - 点击"确定"

验证要求：
- C3单元格（12000）应显示红色背景
- C5单元格（15000）应显示红色背景
- 其他销售额单元格保持原样

💡 提示：
- 条件格式会根据数据变化自动更新
- 可以设置多个条件格式规则`,
        validation: JSON.stringify({
          type: 'conditionalFormat',
          range: 'C2:C6',
          condition: 'greaterThan',
          value: 10000,
          expectedFormattedCells: ['C3', 'C5'],
          expectedBackgroundColor: '#f05252'
        })
      }
    })

    // 更新复杂条件格式任务
    const complexTaskUpdate = await prisma.task.updateMany({
      where: {
        name: '多条件格式'
      },
      data: {
        description: `学习设置多个条件格式规则

任务说明：
表格中已有学生成绩数据，请设置以下条件格式：
- 成绩≥90：绿色背景
- 成绩60-89：黄色背景
- 成绩<60：红色背景

操作步骤：
1. 选择数据范围C2:C7（成绩数据）
2. 设置第一个条件（优秀）：
   - 点击"数据"菜单中的"条件格式"
   - 选择"突出显示单元格规则" → "大于或等于"
   - 输入90，在"填充"颜色选择框中选择第6列第3行的绿色
   - 点击"确定"
3. 设置第二个条件（及格）：
   - 再次点击"条件格式" → "突出显示单元格规则" → "介于"
   - 输入60到89，在"填充"颜色选择框中选择第5列第3行的黄色
   - 点击"确定"
4. 设置第三个条件（不及格）：
   - 点击"条件格式" → "突出显示单元格规则" → "小于"
   - 输入60，在"填充"颜色选择框中选择第3列第3行的红色
   - 点击"确定"

验证要求：
- 95分、92分应显示绿色背景
- 85分、78分、88分应显示黄色背景
- 45分应显示红色背景

💡 提示：
- 条件格式按优先级执行，后设置的规则优先级更高
- 可以通过"管理规则"调整优先级`,
        validation: JSON.stringify({
          type: 'multiConditionalFormat',
          range: 'C2:C7',
          conditions: [
            { type: 'greaterThanOrEqual', value: 90, color: '#0da471' },
            { type: 'between', minValue: 60, maxValue: 89, color: '#fac815' },
            { type: 'lessThan', value: 60, color: '#f05252' }
          ],
          expectedResults: {
            '#0da471': ['C3', 'C4'],
            '#fac815': ['C2', 'C5', 'C6'],
            '#f05252': ['C7']
          }
        })
      }
    })

    console.log('简单条件格式任务更新结果:', simpleTaskUpdate)
    console.log('复杂条件格式任务更新结果:', complexTaskUpdate)

    return NextResponse.json({
      success: true,
      message: '条件格式任务配置更新成功',
      results: {
        simpleTask: simpleTaskUpdate,
        complexTask: complexTaskUpdate
      }
    })

  } catch (error) {
    console.error('更新条件格式任务配置失败:', error)
    return NextResponse.json({
      success: false,
      message: '更新失败',
      error: error.message
    }, { status: 500 })
  } finally {
    await prisma.$disconnect()
  }
}
