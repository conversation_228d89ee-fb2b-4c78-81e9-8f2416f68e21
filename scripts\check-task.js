// 设置环境变量
process.env.DATABASE_URL = "file:./dev.db"

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkTask() {
  try {
    console.log('Connecting to database...')
    const task = await prisma.task.findFirst({
      where: {
        name: '数值条件格式'
      }
    })

    console.log('Query completed')

    if (task) {
      console.log('Task found!')
      console.log('Task description:', task.description.substring(0, 200) + '...')
      console.log('Task validation:', task.validation)
    } else {
      console.log('Task not found')
    }
  } catch (error) {
    console.error('Error:', error)
  } finally {
    console.log('Disconnecting...')
    await prisma.$disconnect()
  }
}

checkTask()
