// 测试脚本：获取Univer中的条件格式规则并与数据库期望值对比

// 在浏览器控制台中执行的函数
function getConditionalFormatRules() {
  try {
    // 获取Univer实例
    const univerAPI = window.univerAPI;
    if (!univerAPI) {
      console.error('Univer API 未找到');
      return null;
    }

    // 获取当前工作表
    const workbook = univerAPI.getActiveWorkbook();
    if (!workbook) {
      console.error('未找到活动工作簿');
      return null;
    }

    const worksheet = workbook.getActiveSheet();
    if (!worksheet) {
      console.error('未找到活动工作表');
      return null;
    }

    console.log('🔍 尝试多种方法获取条件格式规则...\n');

    // 方法1：通过范围获取条件格式规则
    console.log('方法1：通过FRange.getConditionalFormattingRules()');
    try {
      const range = worksheet.getRange('C2:C7');
      const conditionalRules1 = range.getConditionalFormattingRules();
      console.log('方法1结果:', conditionalRules1);
    } catch (error) {
      console.error('方法1失败:', error);
    }

    // 方法2：通过工作表获取条件格式规则
    console.log('\n方法2：通过FWorksheet.getConditionalFormattingRules()');
    try {
      const conditionalRules2 = worksheet.getConditionalFormattingRules();
      console.log('方法2结果:', conditionalRules2);
    } catch (error) {
      console.error('方法2失败:', error);
    }

    // 方法3：检查单元格的样式
    console.log('\n方法3：检查单元格样式');
    try {
      const cellAddresses = ['C2', 'C3', 'C4', 'C5', 'C6', 'C7'];
      const cellStyles = {};

      cellAddresses.forEach(address => {
        try {
          const cell = worksheet.getRange(address);
          const style = cell.getFormat();
          const backgroundColor = cell.getBackground();
          const value = cell.getValue();

          cellStyles[address] = {
            value: value,
            backgroundColor: backgroundColor,
            style: style
          };

          console.log(`${address}: 值=${value}, 背景色=${backgroundColor}`);
        } catch (cellError) {
          console.error(`获取${address}样式失败:`, cellError);
        }
      });

      console.log('所有单元格样式:', cellStyles);
    } catch (error) {
      console.error('方法3失败:', error);
    }

    // 方法4：尝试使用不同的API
    console.log('\n方法4：尝试其他API方法');
    try {
      // 检查是否有其他可用的条件格式相关方法
      console.log('worksheet对象的方法:', Object.getOwnPropertyNames(worksheet.__proto__));

      // 尝试获取工作表的所有条件格式
      if (typeof worksheet.getConditionalFormats === 'function') {
        const formats = worksheet.getConditionalFormats();
        console.log('getConditionalFormats结果:', formats);
      }

      if (typeof worksheet.getConditionalFormatRules === 'function') {
        const rules = worksheet.getConditionalFormatRules();
        console.log('getConditionalFormatRules结果:', rules);
      }
    } catch (error) {
      console.error('方法4失败:', error);
    }

    // 返回基本信息
    return {
      message: '已尝试多种方法获取条件格式规则，请查看控制台输出',
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('获取条件格式规则失败:', error);
    return null;
  }
}

// 对比函数：将获取到的规则与期望值对比
function compareWithExpectedRules(actualRules) {
  // 数据库中期望的条件格式规则
  const expectedConditions = [
    { type: 'greaterThanOrEqual', value: 90, color: '#0da471' }, // 绿色
    { type: 'between', minValue: 60, maxValue: 89, color: '#fac815' }, // 黄色
    { type: 'lessThan', value: 60, color: '#f05252' } // 红色
  ];
  
  console.log('期望的条件格式规则:', expectedConditions);
  console.log('实际获取的规则:', actualRules);
  
  if (!actualRules || actualRules.totalRules === 0) {
    console.log('❌ 没有检测到任何条件格式规则');
    return false;
  }
  
  if (actualRules.totalRules !== expectedConditions.length) {
    console.log(`❌ 规则数量不匹配。期望：${expectedConditions.length}，实际：${actualRules.totalRules}`);
    return false;
  }
  
  // 逐个对比规则
  let allMatched = true;
  for (let i = 0; i < expectedConditions.length; i++) {
    const expected = expectedConditions[i];
    const actual = actualRules.rules[i];
    
    console.log(`\n对比规则 ${i + 1}:`);
    console.log('期望:', expected);
    console.log('实际:', actual);
    
    // 检查条件类型
    let typeMatched = false;
    switch (expected.type) {
      case 'greaterThanOrEqual':
        typeMatched = actual.operator === 'greaterThanOrEqual' && actual.value === expected.value;
        break;
      case 'between':
        typeMatched = actual.operator === 'between' && 
                     actual.minValue === expected.minValue && 
                     actual.maxValue === expected.maxValue;
        break;
      case 'lessThan':
        typeMatched = actual.operator === 'lessThan' && actual.value === expected.value;
        break;
    }
    
    // 检查颜色
    let colorMatched = false;
    if (actual.style && actual.style.bg && actual.style.bg.rgb) {
      const actualColor = actual.style.bg.rgb;
      colorMatched = isColorMatch(actualColor, expected.color);
    }
    
    console.log(`条件匹配: ${typeMatched ? '✅' : '❌'}`);
    console.log(`颜色匹配: ${colorMatched ? '✅' : '❌'}`);
    
    if (!typeMatched || !colorMatched) {
      allMatched = false;
    }
  }
  
  console.log(`\n总体结果: ${allMatched ? '✅ 所有规则匹配' : '❌ 存在不匹配的规则'}`);
  return allMatched;
}

// 颜色匹配函数
function isColorMatch(actualColor, expectedColor) {
  // 标准化颜色格式
  const normalizeColor = (color) => {
    if (color.startsWith('rgb(')) {
      // 将 RGB(13,164,113) 转换为 #0DA471
      const match = color.match(/rgb\((\d+),(\d+),(\d+)\)/i);
      if (match) {
        const r = parseInt(match[1]).toString(16).padStart(2, '0');
        const g = parseInt(match[2]).toString(16).padStart(2, '0');
        const b = parseInt(match[3]).toString(16).padStart(2, '0');
        return `#${r}${g}${b}`.toUpperCase();
      }
    }
    return color.toUpperCase();
  };

  const normalizedActual = normalizeColor(actualColor);
  const normalizedExpected = normalizeColor(expectedColor);
  
  console.log(`颜色对比: 实际 ${actualColor} -> ${normalizedActual}, 期望 ${expectedColor} -> ${normalizedExpected}`);
  
  return normalizedActual === normalizedExpected;
}

// 主测试函数
function testConditionalFormatRules() {
  console.log('🔍 开始测试条件格式规则...\n');
  
  const actualRules = getConditionalFormatRules();
  if (!actualRules) {
    console.log('❌ 无法获取条件格式规则');
    return false;
  }
  
  const result = compareWithExpectedRules(actualRules);
  
  console.log('\n📊 测试完成');
  return result;
}

// 导出函数供浏览器控制台使用
if (typeof window !== 'undefined') {
  window.testConditionalFormatRules = testConditionalFormatRules;
  window.getConditionalFormatRules = getConditionalFormatRules;
  window.compareWithExpectedRules = compareWithExpectedRules;
}

console.log('条件格式规则测试脚本已加载');
console.log('使用方法：在浏览器控制台中执行 testConditionalFormatRules()');
