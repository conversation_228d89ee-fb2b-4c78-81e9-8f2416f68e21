// 设置环境变量
process.env.DATABASE_URL = "file:./dev.db";

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function unlockSortLevel() {
  try {
    const userEmail = '<EMAIL>';
    
    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email: userEmail }
    });
    
    if (!user) {
      console.log('用户不存在');
      return;
    }
    
    // 查找数据排序主任务
    const sortMainTask = await prisma.level.findFirst({
      where: {
        name: '数据排序',
        isMainTask: true
      }
    });
    
    if (!sortMainTask) {
      console.log('数据排序主任务不存在');
      return;
    }
    
    // 查找简单排序子任务
    const simpleSortLevel = await prisma.level.findFirst({
      where: {
        name: '简单排序',
        parentId: sortMainTask.id
      }
    });
    
    if (!simpleSortLevel) {
      console.log('简单排序子任务不存在');
      return;
    }
    
    // 检查是否已有进度记录
    const existingProgress = await prisma.userProgress.findUnique({
      where: {
        userId_levelId: {
          userId: user.id,
          levelId: simpleSortLevel.id
        }
      }
    });
    
    if (existingProgress) {
      console.log('简单排序关卡已有进度记录');
    } else {
      // 创建简单排序关卡进度，标记为未完成但可访问
      await prisma.userProgress.create({
        data: {
          userId: user.id,
          levelId: simpleSortLevel.id,
          completed: false,
          score: 0,
          attempts: 0
        }
      });
      console.log('简单排序关卡已解锁');
    }
    
    console.log('排序关卡解锁完成！');
  } catch (error) {
    console.error('解锁失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

unlockSortLevel();
