const { test, expect } = require('@playwright/test');

test.describe('修复后的筛选验证功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到应用首页
    await page.goto('http://localhost:3000');
    
    // 等待页面加载
    await page.waitForLoadState('networkidle');
    
    // 登录（如果需要）
    try {
      // 检查是否在登录页面
      const loginButton = page.locator('button:has-text("登录")');
      if (await loginButton.isVisible({ timeout: 3000 })) {
        // 填写登录信息
        await page.fill('input[type="email"]', '<EMAIL>');
        await page.fill('input[type="password"]', 'password123');
        await loginButton.click();
        await page.waitForTimeout(2000);
      }
    } catch (error) {
      console.log('登录步骤跳过或失败:', error.message);
    }
  });

  test('单列筛选 - 验证初始数据加载', async ({ page }) => {
    // 导航到单列筛选任务
    try {
      await page.click('text=开始学习');
      await page.waitForTimeout(2000);
    } catch (error) {
      console.log('点击开始学习失败，尝试直接导航到任务');
    }
    
    // 查找并点击单列筛选任务
    const filterTask = page.locator('text=单列筛选').first();
    await expect(filterTask).toBeVisible({ timeout: 10000 });
    await filterTask.click();
    
    // 等待Excel组件加载
    await page.waitForTimeout(5000);
    
    // 等待Univer组件完全加载
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(3000);
    
    // 截图记录初始状态
    await page.screenshot({ 
      path: 'screenshots/filter-test-initial-data.png',
      fullPage: true 
    });
    
    // 检查初始数据是否正确加载
    const dataCheck = await page.evaluate(() => {
      if (window.univerAPI) {
        try {
          const workbook = window.univerAPI.getActiveWorkbook();
          const worksheet = workbook.getActiveSheet();
          const range = worksheet.getRange('A1:C6');
          const values = range.getValues();
          return {
            success: true,
            data: values,
            hasData: values && values.length > 0
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      }
      return {
        success: false,
        error: 'univerAPI not available'
      };
    });
    
    console.log('初始数据检查结果:', dataCheck);
    
    // 验证初始数据是否包含期望的员工信息
    if (dataCheck.success && dataCheck.data) {
      const hasExpectedData = dataCheck.data.some(row => 
        row && row.some(cell => 
          typeof cell === 'string' && (cell.includes('张三') || cell.includes('销售部'))
        )
      );
      expect(hasExpectedData).toBe(true);
      console.log('✓ 初始数据验证通过');
    }
  });

  test('单列筛选 - 未设置筛选条件应失败', async ({ page }) => {
    // 导航到单列筛选任务
    try {
      await page.click('text=开始学习');
      await page.waitForTimeout(2000);
    } catch (error) {
      console.log('点击开始学习失败，尝试直接导航到任务');
    }
    
    // 查找并点击单列筛选任务
    const filterTask = page.locator('text=单列筛选').first();
    await expect(filterTask).toBeVisible({ timeout: 10000 });
    await filterTask.click();
    
    // 等待Excel组件加载
    await page.waitForTimeout(5000);
    
    // 等待Univer组件完全加载
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(3000);
    
    // 直接提交任务，应该失败
    const submitButton = page.locator('button:has-text("提交任务")');
    if (await submitButton.isVisible()) {
      await submitButton.click();
      await page.waitForTimeout(3000);
      
      // 应该看到验证失败的消息
      const errorMessage = page.locator('text=未检测到筛选操作');
      await expect(errorMessage).toBeVisible({ timeout: 5000 });
      
      console.log('✓ 验证通过：未设置筛选条件时正确显示错误消息');
    }
  });

  test('多列筛选 - 验证初始数据加载', async ({ page }) => {
    // 导航到多列筛选任务
    try {
      await page.click('text=开始学习');
      await page.waitForTimeout(2000);
    } catch (error) {
      console.log('点击开始学习失败，尝试直接导航到任务');
    }
    
    // 查找并点击多列筛选任务
    const multiFilterTask = page.locator('text=多列筛选').first();
    await expect(multiFilterTask).toBeVisible({ timeout: 10000 });
    await multiFilterTask.click();
    
    // 等待Excel组件加载
    await page.waitForTimeout(5000);
    
    // 等待Univer组件完全加载
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(3000);
    
    // 截图记录初始状态
    await page.screenshot({ 
      path: 'screenshots/multi-filter-test-initial-data.png',
      fullPage: true 
    });
    
    // 检查初始数据是否正确加载
    const dataCheck = await page.evaluate(() => {
      if (window.univerAPI) {
        try {
          const workbook = window.univerAPI.getActiveWorkbook();
          const worksheet = workbook.getActiveSheet();
          const range = worksheet.getRange('A1:D7');
          const values = range.getValues();
          return {
            success: true,
            data: values,
            hasData: values && values.length > 0
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      }
      return {
        success: false,
        error: 'univerAPI not available'
      };
    });
    
    console.log('多列筛选初始数据检查结果:', dataCheck);
    
    // 验证初始数据是否包含期望的产品信息
    if (dataCheck.success && dataCheck.data) {
      const hasExpectedData = dataCheck.data.some(row => 
        row && row.some(cell => 
          typeof cell === 'string' && (cell.includes('笔记本') || cell.includes('北京') || cell.includes('Q1'))
        )
      );
      expect(hasExpectedData).toBe(true);
      console.log('✓ 多列筛选初始数据验证通过');
    }
  });

  test('多列筛选 - 未设置筛选条件应失败', async ({ page }) => {
    // 导航到多列筛选任务
    try {
      await page.click('text=开始学习');
      await page.waitForTimeout(2000);
    } catch (error) {
      console.log('点击开始学习失败，尝试直接导航到任务');
    }
    
    // 查找并点击多列筛选任务
    const multiFilterTask = page.locator('text=多列筛选').first();
    await expect(multiFilterTask).toBeVisible({ timeout: 10000 });
    await multiFilterTask.click();
    
    // 等待Excel组件加载
    await page.waitForTimeout(5000);
    
    // 等待Univer组件完全加载
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(3000);
    
    // 直接提交任务，应该失败
    const submitButton = page.locator('button:has-text("提交任务")');
    if (await submitButton.isVisible()) {
      await submitButton.click();
      await page.waitForTimeout(3000);
      
      // 应该看到验证失败的消息
      const errorMessage = page.locator('text=未检测到筛选操作');
      await expect(errorMessage).toBeVisible({ timeout: 5000 });
      
      console.log('✓ 验证通过：多列筛选未设置筛选条件时正确显示错误消息');
    }
  });

  test('验证筛选功能的数据检测逻辑', async ({ page }) => {
    // 导航到单列筛选任务
    try {
      await page.click('text=开始学习');
      await page.waitForTimeout(2000);
    } catch (error) {
      console.log('点击开始学习失败，尝试直接导航到任务');
    }
    
    const filterTask = page.locator('text=单列筛选').first();
    await expect(filterTask).toBeVisible({ timeout: 10000 });
    await filterTask.click();
    
    // 等待Excel组件加载
    await page.waitForTimeout(5000);
    await page.waitForSelector('canvas', { timeout: 15000 });
    await page.waitForTimeout(3000);
    
    // 测试验证逻辑
    const validationTest = await page.evaluate(() => {
      if (window.univerAPI) {
        try {
          const workbook = window.univerAPI.getActiveWorkbook();
          const worksheet = workbook.getActiveSheet();
          const range = worksheet.getRange('A1:C6');
          const values = range.getValues();
          
          // 模拟期望的筛选结果
          const expectedFilteredData = [
            { 0: '张三', 1: '销售部', 2: 8000 },
            { 0: '王五', 1: '销售部', 2: 9000 }
          ];
          
          // 检查原始数据中是否包含期望的筛选结果
          let matchingRows = [];
          const originalDataRows = values.slice(1); // 跳过表头
          
          for (const expectedRow of expectedFilteredData) {
            for (const actualRow of originalDataRows) {
              let isRowMatch = true;
              
              for (const key in expectedRow) {
                const colIndex = parseInt(key);
                const expectedValue = expectedRow[key];
                const actualValue = actualRow[colIndex];
                
                if (actualValue !== expectedValue) {
                  isRowMatch = false;
                  break;
                }
              }
              
              if (isRowMatch) {
                matchingRows.push(actualRow);
                break;
              }
            }
          }
          
          return {
            success: true,
            originalData: values,
            originalDataRows: originalDataRows,
            expectedFilteredData: expectedFilteredData,
            matchingRows: matchingRows,
            canDetectFilter: matchingRows.length === expectedFilteredData.length && matchingRows.length < originalDataRows.length
          };
        } catch (error) {
          return {
            success: false,
            error: error.message
          };
        }
      }
      return {
        success: false,
        error: 'univerAPI not available'
      };
    });
    
    console.log('筛选检测逻辑测试结果:', validationTest);
    
    if (validationTest.success) {
      expect(validationTest.canDetectFilter).toBe(true);
      console.log('✓ 筛选检测逻辑验证通过');
    }
  });
});
