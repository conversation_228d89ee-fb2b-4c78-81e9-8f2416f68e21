// 测试筛选验证逻辑的脚本
const { ExcelValidationService } = require('../app/lib/validation.ts');

// 模拟Univer API
class MockUniverAPI {
  constructor(testData) {
    this.testData = testData;
  }
  
  getActiveWorkbook() {
    return {
      getActiveSheet: () => ({
        getRange: (range) => ({
          getValues: () => this.testData,
          getValue: () => this.testData[0] && this.testData[0][0],
          getCellData: () => ({ v: this.testData[0] && this.testData[0][0] })
        }),
        hasFilter: () => false,
        getVisibleRows: () => this.testData
      })
    };
  }
}

// 测试单列筛选验证逻辑
async function testSingleColumnFilter() {
  console.log('测试单列筛选验证逻辑...');
  
  // 模拟员工数据
  const employeeData = [
    ['姓名', '部门', '工资'],
    ['张三', '销售部', 8000],
    ['李四', '技术部', 12000],
    ['王五', '销售部', 9000],
    ['赵六', '财务部', 7500],
    ['钱七', '人事部', 6800]
  ];
  
  const mockAPI = new MockUniverAPI(employeeData);
  const validationService = new ExcelValidationService(mockAPI);
  
  // 测试验证规则
  const validationRule = {
    type: 'filter',
    dataRange: 'A1:C6',
    expectedVisibleRows: 2,
    expectedFilteredData: [
      { 0: '张三', 1: '销售部', 2: 8000 },
      { 0: '王五', 1: '销售部', 2: 9000 }
    ]
  };
  
  try {
    const result = await validationService.validateTask(validationRule);
    console.log('单列筛选验证结果:', result);
    
    if (result.success) {
      console.log('✓ 单列筛选验证逻辑正常');
    } else {
      console.log('✗ 单列筛选验证逻辑有问题:', result.message);
    }
  } catch (error) {
    console.error('单列筛选验证错误:', error);
  }
}

// 测试多列筛选验证逻辑
async function testMultiColumnFilter() {
  console.log('测试多列筛选验证逻辑...');
  
  // 模拟产品数据
  const productData = [
    ['产品', '地区', '季度', '销售额'],
    ['笔记本', '北京', 'Q1', 15000],
    ['台式机', '上海', 'Q1', 12000],
    ['笔记本', '广州', 'Q2', 18000],
    ['台式机', '北京', 'Q2', 14000],
    ['平板', '上海', 'Q1', 8000],
    ['手机', '北京', 'Q1', 22000]
  ];
  
  const mockAPI = new MockUniverAPI(productData);
  const validationService = new ExcelValidationService(mockAPI);
  
  // 测试验证规则
  const validationRule = {
    type: 'filter',
    dataRange: 'A1:D7',
    expectedVisibleRows: 2,
    expectedFilteredData: [
      { 0: '笔记本', 1: '北京', 2: 'Q1', 3: 15000 },
      { 0: '手机', 1: '北京', 2: 'Q1', 3: 22000 }
    ]
  };
  
  try {
    const result = await validationService.validateTask(validationRule);
    console.log('多列筛选验证结果:', result);
    
    if (result.success) {
      console.log('✓ 多列筛选验证逻辑正常');
    } else {
      console.log('✗ 多列筛选验证逻辑有问题:', result.message);
    }
  } catch (error) {
    console.error('多列筛选验证错误:', error);
  }
}

// 测试数据匹配逻辑
function testDataMatching() {
  console.log('测试数据匹配逻辑...');
  
  const originalData = [
    ['张三', '销售部', 8000],
    ['李四', '技术部', 12000],
    ['王五', '销售部', 9000],
    ['赵六', '财务部', 7500],
    ['钱七', '人事部', 6800]
  ];
  
  const expectedFilteredData = [
    { 0: '张三', 1: '销售部', 2: 8000 },
    { 0: '王五', 1: '销售部', 2: 9000 }
  ];
  
  let matchingRows = [];
  
  // 查找匹配的行
  for (const expectedRow of expectedFilteredData) {
    for (const actualRow of originalData) {
      let isRowMatch = true;
      
      for (const key in expectedRow) {
        const colIndex = parseInt(key);
        const expectedValue = expectedRow[key];
        const actualValue = actualRow[colIndex];
        
        if (actualValue !== expectedValue) {
          isRowMatch = false;
          break;
        }
      }
      
      if (isRowMatch) {
        const isDuplicate = matchingRows.some(row => 
          row.every((cell, idx) => cell === actualRow[idx])
        );
        if (!isDuplicate) {
          matchingRows.push(actualRow);
        }
        break;
      }
    }
  }
  
  console.log('原始数据:', originalData);
  console.log('期望筛选结果:', expectedFilteredData);
  console.log('找到的匹配行:', matchingRows);
  
  const isCorrect = matchingRows.length === expectedFilteredData.length && 
                   matchingRows.length < originalData.length;
  
  if (isCorrect) {
    console.log('✓ 数据匹配逻辑正常');
  } else {
    console.log('✗ 数据匹配逻辑有问题');
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('开始测试筛选验证逻辑...\n');
  
  testDataMatching();
  console.log('');
  
  await testSingleColumnFilter();
  console.log('');
  
  await testMultiColumnFilter();
  console.log('');
  
  console.log('所有测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testSingleColumnFilter,
  testMultiColumnFilter,
  testDataMatching,
  runAllTests
};
