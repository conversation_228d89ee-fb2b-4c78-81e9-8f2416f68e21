const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 模拟验证逻辑测试
function testFilterValidationLogic() {
  console.log('测试筛选验证逻辑...\n');
  
  // 测试单列筛选数据匹配
  console.log('=== 单列筛选测试 ===');
  const employeeData = [
    ['姓名', '部门', '工资'],
    ['张三', '销售部', 8000],
    ['李四', '技术部', 12000],
    ['王五', '销售部', 9000],
    ['赵六', '财务部', 7500],
    ['钱七', '人事部', 6800]
  ];
  
  const expectedSingleFilter = [
    { 0: '张三', 1: '销售部', 2: 8000 },
    { 0: '王五', 1: '销售部', 2: 9000 }
  ];
  
  console.log('原始数据:', employeeData);
  console.log('期望筛选结果:', expectedSingleFilter);
  
  // 模拟筛选检测逻辑
  const originalDataRows = employeeData.slice(1); // 跳过表头
  let matchingRows = [];
  
  for (const expectedRow of expectedSingleFilter) {
    for (const actualRow of originalDataRows) {
      let isRowMatch = true;
      
      for (const key in expectedRow) {
        const colIndex = parseInt(key);
        const expectedValue = expectedRow[key];
        const actualValue = actualRow[colIndex];
        
        if (actualValue !== expectedValue) {
          isRowMatch = false;
          break;
        }
      }
      
      if (isRowMatch) {
        const isDuplicate = matchingRows.some(row => 
          row.every((cell, idx) => cell === actualRow[idx])
        );
        if (!isDuplicate) {
          matchingRows.push(actualRow);
        }
        break;
      }
    }
  }
  
  console.log('找到的匹配行:', matchingRows);
  
  const canDetectSingleFilter = matchingRows.length === expectedSingleFilter.length && 
                               matchingRows.length < originalDataRows.length;
  
  console.log('单列筛选检测结果:', canDetectSingleFilter ? '✓ 通过' : '✗ 失败');
  console.log('匹配行数:', matchingRows.length, '期望行数:', expectedSingleFilter.length, '原始行数:', originalDataRows.length);
  
  // 测试多列筛选数据匹配
  console.log('\n=== 多列筛选测试 ===');
  const productData = [
    ['产品', '地区', '季度', '销售额'],
    ['笔记本', '北京', 'Q1', 15000],
    ['台式机', '上海', 'Q1', 12000],
    ['笔记本', '广州', 'Q2', 18000],
    ['台式机', '北京', 'Q2', 14000],
    ['平板', '上海', 'Q1', 8000],
    ['手机', '北京', 'Q1', 22000]
  ];
  
  const expectedMultiFilter = [
    { 0: '笔记本', 1: '北京', 2: 'Q1', 3: 15000 },
    { 0: '手机', 1: '北京', 2: 'Q1', 3: 22000 }
  ];
  
  console.log('原始数据:', productData);
  console.log('期望筛选结果:', expectedMultiFilter);
  
  const originalProductRows = productData.slice(1); // 跳过表头
  let matchingProductRows = [];
  
  for (const expectedRow of expectedMultiFilter) {
    for (const actualRow of originalProductRows) {
      let isRowMatch = true;
      
      for (const key in expectedRow) {
        const colIndex = parseInt(key);
        const expectedValue = expectedRow[key];
        const actualValue = actualRow[colIndex];
        
        if (actualValue !== expectedValue) {
          isRowMatch = false;
          break;
        }
      }
      
      if (isRowMatch) {
        const isDuplicate = matchingProductRows.some(row => 
          row.every((cell, idx) => cell === actualRow[idx])
        );
        if (!isDuplicate) {
          matchingProductRows.push(actualRow);
        }
        break;
      }
    }
  }
  
  console.log('找到的匹配行:', matchingProductRows);
  
  const canDetectMultiFilter = matchingProductRows.length === expectedMultiFilter.length && 
                              matchingProductRows.length < originalProductRows.length;
  
  console.log('多列筛选检测结果:', canDetectMultiFilter ? '✓ 通过' : '✗ 失败');
  console.log('匹配行数:', matchingProductRows.length, '期望行数:', expectedMultiFilter.length, '原始行数:', originalProductRows.length);
  
  return {
    singleFilterPassed: canDetectSingleFilter,
    multiFilterPassed: canDetectMultiFilter
  };
}

// 检查数据库中的任务配置
async function checkTaskConfiguration() {
  console.log('\n=== 检查数据库任务配置 ===');
  
  try {
    // 查找筛选任务
    const filterTasks = await prisma.task.findMany({
      where: {
        OR: [
          { name: '单列筛选' },
          { name: '多列筛选' }
        ]
      },
      include: {
        level: true
      }
    });
    
    console.log(`找到 ${filterTasks.length} 个筛选任务:`);
    
    for (const task of filterTasks) {
      console.log(`\n任务: ${task.name}`);
      console.log(`ID: ${task.id}`);
      console.log(`类型: ${task.type}`);
      console.log(`关卡: ${task.level.name}`);
      
      try {
        const validation = JSON.parse(task.validation);
        console.log('验证规则:', validation);
        
        if (task.initialData) {
          const initialData = JSON.parse(task.initialData);
          console.log('初始数据:', initialData);
        }
      } catch (error) {
        console.log('解析验证规则失败:', error.message);
      }
    }
    
    return filterTasks;
  } catch (error) {
    console.error('查询数据库失败:', error);
    return [];
  }
}

// 验证任务配置是否正确
function validateTaskConfiguration(tasks) {
  console.log('\n=== 验证任务配置 ===');
  
  const singleFilterTask = tasks.find(t => t.name === '单列筛选');
  const multiFilterTask = tasks.find(t => t.name === '多列筛选');
  
  let configurationValid = true;
  
  if (singleFilterTask) {
    try {
      const validation = JSON.parse(singleFilterTask.validation);
      const initialData = JSON.parse(singleFilterTask.initialData || '{}');
      
      console.log('单列筛选任务配置检查:');
      console.log('- 验证类型:', validation.type === 'filter' ? '✓' : '✗');
      console.log('- 数据范围:', validation.dataRange === 'A1:C6' ? '✓' : '✗');
      console.log('- 期望行数:', validation.expectedVisibleRows === 2 ? '✓' : '✗');
      console.log('- 期望数据:', validation.expectedFilteredData ? '✓' : '✗');
      console.log('- 初始数据:', Object.keys(initialData).length > 0 ? '✓' : '✗');
      
      if (validation.type !== 'filter' || validation.dataRange !== 'A1:C6' || validation.expectedVisibleRows !== 2) {
        configurationValid = false;
      }
    } catch (error) {
      console.log('单列筛选任务配置解析失败:', error.message);
      configurationValid = false;
    }
  } else {
    console.log('✗ 未找到单列筛选任务');
    configurationValid = false;
  }
  
  if (multiFilterTask) {
    try {
      const validation = JSON.parse(multiFilterTask.validation);
      const initialData = JSON.parse(multiFilterTask.initialData || '{}');
      
      console.log('\n多列筛选任务配置检查:');
      console.log('- 验证类型:', validation.type === 'filter' ? '✓' : '✗');
      console.log('- 数据范围:', validation.dataRange === 'A1:D7' ? '✓' : '✗');
      console.log('- 期望行数:', validation.expectedVisibleRows === 2 ? '✓' : '✗');
      console.log('- 期望数据:', validation.expectedFilteredData ? '✓' : '✗');
      console.log('- 初始数据:', Object.keys(initialData).length > 0 ? '✓' : '✗');
      
      if (validation.type !== 'filter' || validation.dataRange !== 'A1:D7' || validation.expectedVisibleRows !== 2) {
        configurationValid = false;
      }
    } catch (error) {
      console.log('多列筛选任务配置解析失败:', error.message);
      configurationValid = false;
    }
  } else {
    console.log('✗ 未找到多列筛选任务');
    configurationValid = false;
  }
  
  return configurationValid;
}

// 主测试函数
async function runTests() {
  console.log('开始筛选功能修复验证...\n');
  
  // 测试验证逻辑
  const logicTest = testFilterValidationLogic();
  
  // 检查数据库配置
  const tasks = await checkTaskConfiguration();
  
  // 验证任务配置
  const configValid = validateTaskConfiguration(tasks);
  
  console.log('\n=== 测试总结 ===');
  console.log('验证逻辑测试:');
  console.log('- 单列筛选:', logicTest.singleFilterPassed ? '✓ 通过' : '✗ 失败');
  console.log('- 多列筛选:', logicTest.multiFilterPassed ? '✓ 通过' : '✗ 失败');
  console.log('任务配置:', configValid ? '✓ 正确' : '✗ 有问题');
  
  const allPassed = logicTest.singleFilterPassed && logicTest.multiFilterPassed && configValid;
  console.log('\n总体结果:', allPassed ? '✓ 所有测试通过' : '✗ 存在问题需要修复');
  
  await prisma.$disconnect();
  
  return allPassed;
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testFilterValidationLogic, checkTaskConfiguration, validateTaskConfiguration };
