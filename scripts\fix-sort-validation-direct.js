// 设置环境变量
process.env.DATABASE_URL = "file:./dev.db";

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixSortValidation() {
  try {
    console.log('开始修复排序任务的验证规则...');
    
    // 修复单列排序任务的验证规则
    const singleSortTask = await prisma.task.findFirst({
      where: {
        name: '单列排序'
      }
    });
    
    if (singleSortTask) {
      console.log('修复单列排序任务验证规则...');
      
      const validationRule = {
        type: 'sort',
        dataRange: 'A1:C6',
        column: 'C',
        direction: 'desc',
        expectedOrder: ['李四', '王五', '钱七', '张三', '赵六'] // 按成绩降序排列：95, 92, 88, 85, 78
      };
      
      await prisma.task.update({
        where: {
          id: singleSortTask.id
        },
        data: {
          validation: JSON.stringify(validationRule)
        }
      });
      
      console.log('单列排序任务验证规则已修复');
    }
    
    console.log('排序任务验证规则修复完成！');
  } catch (error) {
    console.error('修复失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixSortValidation();
